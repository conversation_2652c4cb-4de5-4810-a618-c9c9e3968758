#!/bin/bash

# Script per aggiornare tutti i nomi dei comandi e job

echo "🔄 Aggiornamento nomi comandi e job..."

# Array dei file da aggiornare
declare -A commands=(
    ["ImportSuppliers"]="suppliers:import"
    ["ImportClients"]="clients:import"
    ["ImportCollections"]="collections:import"
    ["ImportDiscountGroups"]="discount-groups:import"
    ["ImportClientAddresses"]="client-addresses:import"
    ["ImportBrandContacts"]="brand-contacts:import"
    ["ImportClientBrands"]="client-brands:import"
    ["ImportClientContacts"]="client-contacts:import"
    ["ImportClientDiscountGroups"]="client-discount-groups:import"
    ["ImportPartners"]="partners:import"
    ["ImportPartnerAddresses"]="partner-addresses:import"
    ["ImportPartnerBrands"]="partner-brands:import"
    ["ImportPartnerContacts"]="partner-contacts:import"
    ["ImportPartnerDiscountGroups"]="partner-discount-groups:import"
    ["ImportSupplierAddresses"]="supplier-addresses:import"
)

# Aggiorna i comandi
for cmd in "${!commands[@]}"; do
    file="app/Console/Commands/Sync/${cmd}.php"
    if [ -f "$file" ]; then
        echo "📝 Aggiornando $file..."
        
        # Aggiorna il nome della classe
        sed -i '' "s/class ${cmd}FromExcel extends Command/class ${cmd} extends Command/g" "$file"
        
        # Aggiorna il signature
        signature="${commands[$cmd]}"
        sed -i '' "s/${signature}-from-excel/${signature}/g" "$file"
        
        # Aggiorna gli import dei job
        sed -i '' "s/FromExcelRowJob/Job/g" "$file"
        
        # Aggiorna i dispatch dei job
        sed -i '' "s/FromExcelRowJob::/Job::/g" "$file"
    fi
done

# Aggiorna i job
for job_file in app/Jobs/Import*Job.php; do
    if [ -f "$job_file" ]; then
        echo "📝 Aggiornando $job_file..."
        
        # Aggiorna il nome della classe nei job
        basename=$(basename "$job_file" .php)
        old_name="${basename}FromExcelRow"
        new_name="$basename"
        
        sed -i '' "s/class ${old_name}Job implements ShouldQueue/class ${new_name} implements ShouldQueue/g" "$job_file"
        sed -i '' "s/${old_name}Job failed/${new_name} failed/g" "$job_file"
    fi
done

echo "✅ Aggiornamento completato!"
