<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Schedule::command('app:delete-products')->dailyAt('01:00');
Schedule::command('app:sync-products')->dailyAt('02:00');

Schedule::command('scout:import "App\Models\Product"')->dailyAt('06:30');
Schedule::command('scout:import "App\Models\Brand"')->dailyAt('06:30');
Schedule::command('scout:import "App\Models\Collection"')->dailyAt('06:30');
