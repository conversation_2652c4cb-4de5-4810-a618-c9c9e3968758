<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Delete products that are no longer available
Schedule::command('app:delete-products')->dailyAt('01:00');

// Sync products from the source
Schedule::command('app:sync-products')->dailyAt('02:00');

// Sync brand tags from products
Schedule::command('brands:sync-tags-from-products')->dailyAt('04:00');

// Flush meilisearch indexes and data
Schedule::command('scout:flush "App\Models\Product"')->dailyAt('06:00');
Schedule::command('scout:flush "App\Models\Brand"')->dailyAt('06:00');
Schedule::command('scout:flush "App\Models\Collection"')->dailyAt('06:00');

// Sync meilisearch data
Schedule::command('scout:import "App\Models\Product"')->dailyAt('06:30');
Schedule::command('scout:import "App\Models\Brand"')->dailyAt('06:30');
Schedule::command('scout:import "App\Models\Collection"')->dailyAt('06:30');
