<flux:modal name="cart" variant="flyout" class="w-full pt-16" wire:close="refreshAll">
    <div class="flex flex-col gap-8">
        <div>
            <flux:heading size="xl">Cart Details</flux:heading>
            <flux:subheading>Selected cart group: {{ $selectedCartGroup->name ?? 'None.' }}</flux:subheading>
        </div>

        <div class="flex flex-col gap-12">
            {{-- Create a Cart Group --}}
            @can('create', \App\Models\Order::class)
            <form wire:submit="saveCartGroup" class="flex flex-col md:w-1/5">
                <flux:input wire:model="createCartGroupForm.name" type="text" placeholder="Create a new cart group" size="sm" variant="filled" :disabled="$editCartGroupForm->isEditMode">
                    <x-slot name="iconTrailing">
                        <flux:button type="submit" size="xs" variant="subtle" icon="plus" class="-mr-1" :disabled="$editCartGroupForm->isEditMode" />
                    </x-slot>
                </flux:input>

                <flux:error name="createCartGroupForm.name" />
            </form>
            @endcan

            {{-- Cart Groups Listing and Items --}}
            @if ($order && !$order->cartGroups->isEmpty())
                @foreach ($order->cartGroups->sortBy('sort') as $cartGroup)
                    <div class="flex flex-col gap-2">
                        <div>
                            <div class="flex items-center gap-2">
                                {{-- Edit a Cart Group --}}
                                    @if ($editCartGroupForm->isEditMode && $editCartGroupForm->cartGroup->id === $cartGroup->id)
                                        @can('update', $order)
                                            <form wire:submit="updateCartGroup" class="flex flex-col md:w-1/5">
                                                <flux:input wire:model="editCartGroupForm.name" type="text" placeholder="Edit a cart group" size="sm" variant="filled">
                                                    <x-slot name="iconTrailing" class="flex gap-2">
                                                        <flux:button type="submit" size="xs" variant="subtle" icon="check" class="-mr-1" />
                                                        <flux:button type="button" wire:click="cancelEditCartGroup" size="xs" variant="subtle" icon="x-mark" class="-mr-1" />
                                                    </x-slot>
                                                </flux:input>

                                                <flux:error name="editCartGroupForm.name" />
                                            </form>
                                        @endcan
                                    @else
                                        <flux:heading size="lg">{{ $cartGroup->name ?? '' }}</flux:heading>

                                        @can('update', $order)
                                            <flux:dropdown inset>
                                                <flux:button size="xs" icon="ellipsis-vertical" square />

                                                <flux:menu>
                                                    <flux:menu.item wire:click="selectCartGroup({{ $cartGroup?->id }})" icon="plus" :disabled="$cartGroup?->id === $selectedCartGroup?->id">
                                                        {{ $cartGroup?->id === $selectedCartGroup?->id ? 'Selected' : 'Select' }}
                                                    </flux:menu.item>

                                                    @can('update', $order)
                                                        <flux:menu.item wire:click="editCartGroup({{ $cartGroup?->id }})" icon="pencil-square">Edit</flux:menu.item>
                                                    @endcan
                                                    @can('delete', $order)
                                                        <flux:menu.separator />
                                                        <flux:menu.item wire:click="deleteCartGroup({{ $cartGroup?->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                                    @endcan
                                                </flux:menu>
                                            </flux:dropdown>
                                        @endcan
                                    @endif
                            </div>

                            <flux:separator variant="subtle" class="my-2" />
                        </div>

                        {{-- Items Listing --}}
                        @if ($cartGroup->orderRows->isEmpty())
                            <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
                        @else
                            <flux:table>
                                <flux:table.columns>
                                    <flux:table.column></flux:table.column>
                                    <flux:table.column></flux:table.column>
                                    <flux:table.column>Description</flux:table.column>

                                    <flux:table.column>Quantity</flux:table.column>
                                    <flux:table.column>Price</flux:table.column>
                                    <flux:table.column>Discount (%)</flux:table.column>
                                    <flux:table.column>Variation (%)</flux:table.column>
                                    <flux:table.column>Exp. Delivery</flux:table.column>
                                    <flux:table.column>Unit Price</flux:table.column>
                                    <flux:table.column>Final Price</flux:table.column>
                                </flux:table.columns>

                                <flux:table.rows x-sort="$wire.sort($item, $position)">
                                    @foreach ($cartGroup->orderRows->sortBy('sort') as $row)
                                        <livewire:components.orders.creator.order-row :resourceValue="$order" :row="$row" :key="$row->id" />
                                    @endforeach
                                </flux:table.rows>
                            </flux:table>
                        @endif
                    </div>
                @endforeach
            @endif

            {{-- Create Custom Products --}}
            @can('create', \App\Models\Order::class)
                <div class="flex flex-col gap-2">
                    <flux:heading size="lg">Custom Products</flux:heading>
                    <flux:separator variant="subtle" class="my-2" />

                    <form wire:submit="saveCustomProduct">
                        <flux:table>
                            <flux:table.columns>
                                <flux:table.column>SKU</flux:table.column>
                                <flux:table.column>Description</flux:table.column>
                                <flux:table.column>Brand</flux:table.column>
                                <flux:table.column>Dimensions</flux:table.column>
                                <flux:table.column>Color</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows>
                                <flux:table.row>
                                    <form wire:submit="saveCustomOrderRow" class="flex flex-col gap-2">
                                        <flux:table.cell>
                                            <flux:input wire:model="createCustomProductForm.sku" type="text" placeholder="SKU" size="sm" variant="filled" />
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:input wire:model="createCustomProductForm.description" type="text" placeholder="Description" size="sm" variant="filled" />
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:input wire:model="createCustomProductForm.brand_name" type="text" placeholder="Brand" size="sm" variant="filled" />
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:input wire:model="createCustomProductForm.dimensions" type="text" placeholder="Dimensions" size="sm" variant="filled" />
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:input wire:model="createCustomProductForm.supplier_color" type="text" placeholder="Color" size="sm" variant="filled" />
                                        </flux:table.cell>

                                        {{-- Actions --}}
                                        <flux:table.cell>
                                            <div class="flex justify-end gap-2">
                                                <flux:button type="submit" size="sm" icon="plus" square></flux:button>
                                            </div>
                                        </flux:table.cell>
                                    </form>
                                </flux:table.row>
                            </flux:table.rows>
                        </flux:table>
                    </form>
                </div>
            @endcan

            {{-- Create Addons --}}
            <div class="flex flex-col gap-2">
                <flux:heading size="lg">Addons</flux:heading>
                <flux:separator variant="subtle" class="my-2" />

                @can('create', \App\Models\Order::class)
                    <form wire:submit="saveAddon" class="flex">
                        <div class="flex gap-2">
                            <div>
                                <flux:input wire:model="createAddonForm.code" type="text" placeholder="Addon code" size="sm" variant="filled" :disabled="$createAddonForm->isEditMode" />
                                <flux:error name="createAddonForm.code" />
                            </div>
                            <div>
                                <flux:input wire:model="createAddonForm.description" type="text" placeholder="Addon description" size="sm" variant="filled" :disabled="$createAddonForm->isEditMode" />
                                <flux:error name="createAddonForm.description" />
                            </div>
                            <div>
                                <flux:input wire:model="createAddonForm.percent" type="number" placeholder="Addon percent" size="sm" variant="filled" :disabled="$createAddonForm->isEditMode" />
                                <flux:error name="createAddonForm.percent" />
                            </div>
                            <flux:button type="submit" size="sm" icon="plus" :disabled="$editAddonForm->isEditMode">Create</flux:button>
                        </div>
                    </form>
                @endcan

                {{-- Addons Listing --}}
                @if ($order && !$order->addons->isEmpty())
                    @if ($order->addons()->get()->isEmpty())
                        <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
                    @else
                        <form wire:submit="updateAddon">
                            <flux:table>
                                <flux:table.columns>
                                    <flux:table.column>Code</flux:table.column>
                                    <flux:table.column>Description</flux:table.column>
                                    <flux:table.column>Percent (%)</flux:table.column>
                                </flux:table.columns>

                                <flux:table.rows>
                                    @foreach ($order->addons as $addon)

                                        {{-- Edit Addon --}}
                                        @if ($editAddonForm->isEditMode && $editAddonForm->orderAddon->id === $addon->id)
                                            <flux:table.row>
                                                <flux:table.cell>
                                                    <flux:input wire:model="editAddonForm.code" type="text" placeholder="Addon code" size="sm" variant="filled" />
                                                    <flux:error name="editAddonForm.code" />
                                                </flux:table.cell>
                                                <flux:table.cell>
                                                    <flux:input wire:model="editAddonForm.description" type="text" placeholder="Addon description" size="sm" variant="filled" />
                                                    <flux:error name="editAddonForm.description" />
                                                </flux:table.cell>
                                                <flux:table.cell>
                                                    <flux:input wire:model="editAddonForm.percent" type="number" placeholder="Addon percent" size="sm" variant="filled" />
                                                    <flux:error name="editAddonForm.percent" />
                                                </flux:table.cell>

                                                {{-- Actions --}}
                                                <flux:table.cell>
                                                    <div class="flex justify-end gap-2">
                                                        <flux:button type="submit" size="sm" variant="subtle" icon="check" class="-mr-1" />
                                                        <flux:button type="button" wire:click="cancelEditAddon" size="sm" variant="subtle" icon="x-mark" class="" />
                                                    </div>
                                                </flux:table.cell>
                                            </flux:table.row>
                                        @else

                                            {{-- View Addon --}}
                                            <flux:table.row>
                                                <flux:table.cell>{{ $addon->code ?? '-' }}</flux:table.cell>
                                                <flux:table.cell>{{ $addon->description ?? '-' }}</flux:table.cell>
                                                <flux:table.cell>{{ $addon->percent ?? '--' }} %</flux:table.cell>

                                                {{-- Actions Dropdown --}}
                                                <flux:table.cell>
                                                    <div class="flex justify-end">
                                                        <flux:dropdown>
                                                            <flux:button size="sm" icon="ellipsis-vertical" square />

                                                            <flux:menu>
                                                                <flux:menu.item wire:click="" icon="eye" disabled>View</flux:menu.item>
                                                                @can('update', $order)
                                                                    <flux:menu.item wire:click="editAddon({{ $addon?->id }})" icon="pencil-square">Edit</flux:menu.item>
                                                                @endcan
                                                                @can('delete', $order)
                                                                    <flux:menu.separator />
                                                                    <flux:menu.item wire:confirm="Are you sure you want to delete this addon? This action cannot be undone." wire:click="deleteAddon({{ $addon->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                                                @endcan
                                                            </flux:menu>
                                                        </flux:dropdown>
                                                    </div>
                                                </flux:table.cell>
                                            </flux:table.row>
                                        @endif
                                    @endforeach
                                </flux:table.rows>
                            </flux:table>
                        </form>
                    @endif
                @endif

            </div>
        </div>
    </div>
</flux:modal>
