<flux:modal name="add-to-cart" class="w-full md:max-w-xl lg:max-w-2xl" @close="resetAddToCart">
    <div class="flex flex-col gap-4 p-4">

        {{-- Header --}}
        <div>
            <flux:heading class="">Add to cart</flux:heading>
            <flux:subheading class="">Choose details and add to cart</flux:subheading>
        </div>

        {{-- Body --}}
        <div class="flex flex-col gap-2">

            {{-- Modules --}}
            @if ($product?->hasModules())
                @foreach ($product?->modules as $module)
                    <div class="flex flex-col gap-2" wire:key="module-{{ $module->id }}">
                        <flux:heading class="mt-6">Step {{ $loop->iteration }}</flux:heading>

                        <flux:select wire:model.live="selectedCategories.{{ $loop->iteration - 1 }}" variant="listbox" searchable clearable placeholder="Choose category ...">
                            @foreach($this->fetchCategories($module) as $category)
                                <flux:select.option value="{{ $category }}" wire:key="module-{{ $module->id }}-category-{{ $category }}">{{ $category }}</flux:select.option>
                            @endforeach
                        </flux:select>
                        
                        <flux:select wire:model.live="selectedOptions.{{ $loop->iteration - 1 }}" variant="listbox" searchable clearable placeholder="Choose option ..." :disabled="empty($selectedCategories[$loop->iteration - 1])">
                            @foreach($this->fetchOptions($selectedCategories[$loop->iteration - 1]) as $option)
                                <flux:select.option value="{{ $option->id }}" wire:key="module-{{ $module->id }}-option-{{ $option->id }}">
                                    <div class="flex items-center gap-2">
                                        <flux:avatar circle size="xs" src="{{ Storage::disk(config('filesystems.public'))->url($option->image ?? '') }}" />
                                        
                                        {{ $option->description }}
                                    </div>
                                </flux:select.option>
                            @endforeach
                        </flux:select>

                        <flux:error name="selectedOptions.{{ $loop->iteration - 1 }}" />
                    </div>
                @endforeach
            @endif

            {{-- Quantity --}}
            <div class="mt-6">
                <flux:input wire:model="selectedQuantity" type="number" icon="hashtag" placeholder="0" variant="filled" label="Quantity" :description="'Select the quantity.' . ($product?->purchase_units > 1 ? ' | Minimum purchase quantity is ' . $product?->purchase_units : '')" />
            </div>
        </div>

        {{-- Actions - Add to cart --}}
        <flux:button wire:click="addToCart" icon="shopping-bag" variant="primary" size="sm" class="w-full">Add to cart</flux:button>
    </div>
</flux:modal>
