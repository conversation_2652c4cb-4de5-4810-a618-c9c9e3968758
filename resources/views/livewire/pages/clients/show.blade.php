<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Client: {{ $client->company }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the client info and details</flux:subheading>

    <div class="flex flex-col gap-12 my-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    {{-- <flux:button wire:click="inheritBrands" size="sm">Inherit brands</flux:button> --}}
                    @canany(['update', 'delete'], $client)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $client)
                                    <flux:menu.item wire:click="edit({{ $client->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan
                                @can('delete', $client)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this client? This action cannot be undone." wire:click="delete({{ $client->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Client details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the client info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model="form.type" disabled variant="listbox" placeholder="Choose type..." label="Type" description="This will be publicly displayed.">
                        @foreach($clientTypes as $clientType)
                            <flux:select.option value="{{ $clientType->value }}">{{ $clientType->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:input wire:model="form.company" readonly variant="filled" label="Company" description="This will be publicly displayed." />

                    <flux:input wire:model="form.email" readonly copyable variant="filled" label="Email" description="This will be publicly displayed." />
                    <flux:select searchable wire:model="form.partner_id" variant="listbox" disabled placeholder="Choose partner..." label="Partner" description="This will be publicly displayed." badge="Optional">
                        @foreach($partners as $partner)
                            <flux:select.option value="{{ $partner->id }}">{{ $partner->company }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:select searchable wire:model="form.internal_referent_id" variant="listbox" disabled placeholder="Choose internal referent..." label="Internal Referent" description="This will be publicly displayed." badge="Optional">
                        @foreach($internalReferents as $internalReferent)
                            <flux:select.option value="{{ $internalReferent->id }}">{{ $internalReferent->first_name . ' ' . $internalReferent->last_name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:select searchable wire:model="form.area_manager_id" variant="listbox" disabled placeholder="Choose area manager..." label="Area Manager" description="This will be publicly displayed." badge="Optional">
                        @foreach($areaManagers as $areaManager)
                            <flux:select.option value="{{ $areaManager->id }}">{{ $areaManager->first_name . ' ' . $areaManager->last_name }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:select searchable wire:model="form.country" variant="listbox" disabled searchable placeholder="Choose country for the client..." label="Country" description="This will be publicly displayed." badge="Optional">
                        @foreach($countries as $country)
                            <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:field>
                        <flux:select searchable wire:model.live="form.countries_of_expertise" variant="listbox" disabled searchable multiple placeholder="Choose countries of expertise..." label="Countries of Expertise" description="This will be publicly displayed." badge="Optional">
                            @foreach ($countries_of_expertise as $country_of_expertise)
                                <flux:select.option value="{{ $country_of_expertise->value }}">{{ str_replace('_', ' ', $country_of_expertise->name) }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </flux:field>

                    <flux:select searchable wire:model="form.payment_term_id" variant="listbox" disabled placeholder="Choose payment term for the client..." label="Payment Term" description="This will be publicly displayed." badge="Optional">
                        @foreach($paymentTerms as $paymentTerm)
                            <flux:select.option value="{{ $paymentTerm->id }}">{{ $paymentTerm->code . ' - ' . $paymentTerm->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Client details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the client info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model="form.commercial_category" disabled variant="listbox" placeholder="Choose client commercial category..." label="Commercial Category" description="This will be publicly displayed." badge="Optional">
                        @foreach($commercialCategories as $commercialCategory)
                            <flux:select.option value="{{ $commercialCategory->value }}">{{ $commercialCategory->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:radio.group wire:model="form.priority" disabled label="Priority" variant="segmented" badge="Optional" description="This will be publicly displayed.">
                        @foreach ($clientPriorities as $clientPriority)
                            <flux:radio value="{{ $clientPriority->value }}" label="{{ $clientPriority->label() }}" description="This will be publicly displayed." />
                        @endforeach
                    </flux:radio.group>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Sales info</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the sales info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.minimum_orderable" readonly type="number" step="0.01" icon="currency-euro" placeholder="Minimum Orderable" variant="filled" label="Minimum Orderable" description="This will be publicly displayed." badge="Optional" />
                    <flux:input wire:model="form.handling_and_packing" readonly type="number" icon="receipt-percent" placeholder="Handling and Packing" variant="filled" label="Handling and Packing" description="This will be publicly displayed." badge="Optional" />
                    <flux:select searchable wire:model="form.delivery_terms" disabled variant="listbox" placeholder="Choose client delivery terms..." label="Delivery Terms" description="This will be publicly displayed." badge="Optional">
                        @foreach($deliveryTerms as $deliveryTerm)
                            <flux:select.option value="{{ $deliveryTerm->value }}">{{ $deliveryTerm->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:textarea wire:model="form.notes" readonly variant="filled" label="Notes" badge="Optional" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

    </div>

    <flux:separator variant="subtle" class="my-12" />

    {{-- Orders Listing --}}
    <livewire:components.inner-table.orders :resourceType="class_basename($client)" :resourceValue="$client" />

    <flux:separator variant="subtle" class="my-12" />

    {{-- Addresses Listing --}}
    <livewire:components.inner-table.addresses :resourceType="class_basename($client)" :resourceValue="$client" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Contacts Listing --}}
    <livewire:components.inner-table.contacts :resourceType="class_basename($client)" :resourceValue="$client" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Brands Listing --}}
    <livewire:components.inner-table.client-partner-brands :resourceType="class_basename($client)" :resourceValue="$client" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Discount Group Listing --}}
    <livewire:components.inner-table.client-partner-discount-groups :resourceType="class_basename($client)" :resourceValue="$client" >

</flux:main>
