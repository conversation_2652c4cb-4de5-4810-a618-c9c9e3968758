<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Create a new contact</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the contact info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Contact details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the contact info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.name" type="text" placeholder="Name" variant="filled" label="Name" description="This will be publicly displayed." />
                    <div></div>
                    <flux:input wire:model="form.email" type="text" placeholder="Email" icon="envelope" variant="filled" label="Email" description="This will be publicly displayed." />
                    <flux:input wire:model="form.phone" type="text" placeholder="Phone" mask="(+99) ***************" badge="Optional" variant="filled" label="Phone" description="This will be publicly displayed." />
                    <flux:field>
                        <flux:select searchable wire:model.live="form.departments" variant="listbox" clearable multiple placeholder="Choose the contact departments..." label="Departments" description="This will be publicly displayed." badge="Optional">
                            @foreach ($departments as $department)
                                <flux:select.option value="{{ $department->value }}">{{ $department->label() }}</flux:select.option>
                            @endforeach
                        </flux:select>            
                    </flux:field>
                    <flux:input wire:model="form.position" type="text" placeholder="Position" variant="filled" label="Position" description="This will be publicly displayed." badge="Optional" />
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Country and languages details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the contact info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model="form.country" variant="listbox" searchable placeholder="Choose country for the contact..." label="Country" description="This will be publicly displayed." badge="Optional">
                        @foreach($countries as $country)
                            <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:field>
                        <flux:select searchable wire:model.live="form.languages" variant="listbox" searchable clearable multiple placeholder="Choose languages the contact speak..." label="Languages" description="This will be publicly displayed." badge="Optional">
                            @foreach ($languages as $language)
                                <flux:select.option value="{{ $language->value }}">{{ str_replace('_', ' ', $language->name) }}</flux:select.option>
                            @endforeach
                        </flux:select>            
                    </flux:field>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the contact extra info</flux:subheading>
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:textarea wire:model="form.notes" variant="filled" label="Notes" badge="Optional" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-contacts')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>

    </form>

</flux:main>
