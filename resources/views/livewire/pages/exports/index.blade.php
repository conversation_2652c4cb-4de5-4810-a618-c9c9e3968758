<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Exports</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the exports list and details</flux:subheading>

    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {{-- Product XLSX --}}
        <flux:card size="sm">
            <flux:subheading>Product Resources</flux:subheading>
            <flux:button href="#" target="_blank" variant="primary" class="w-full mt-2">Products</flux:button>

            <div class="flex items-center gap-2 mt-4">
                <span class="text-sm">Download related resources:</span>
            </div>
            
            <div class="grid grid-cols-1 gap-2 mt-2">
                <flux:button wire:click="downloadCustomProducts" target="_blank" size="xs">Custom products</flux:button>
                <flux:button wire:click="downloadMissingDiscountGroup" target="_blank" size="xs">Missing discount group</flux:button>
            </div>
        </flux:card>
    </div>

    <div class="hidden flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    {{-- <flux:button href="{{ route('imports.create') }}" wire:navigate size="sm">Create import</flux:button> --}}
                </div>
            </div>
        </div>

        {{-- Imports Listing --}}
        {{-- <flux:table :paginate="$this->users"> --}}
        <flux:table>
            <flux:table.columns>
                <flux:table.column>#</flux:table.column>
                <flux:table.column>Type</flux:table.column>
                <flux:table.column>Status</flux:table.column>
                <flux:table.column>Description</flux:table.column>
                <flux:table.column>Date</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                {{-- @foreach ($this->users as $user) --}}
                <flux:table.row>
                    <flux:table.cell variant="strong">#1</flux:table.cell>
                    <flux:table.cell>
                        <flux:badge color="zinc" icon="truck" size="sm">Suppliers</flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>
                        <flux:badge color="blue" size="sm"><flux:icon.loading variant="micro" class="mr-2" /> Importing</flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>Import iniziale contenuti</flux:table.cell>
                    <flux:table.cell>03-12-24</flux:table.cell>
                    <flux:table.cell class="flex justify-end">
                        <flux:dropdown>
                            <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                            <flux:menu>
                                <flux:menu.item wire:click="downloadXLSX" icon="arrow-down-tray">Download .XLSX</flux:menu.item>
                                <flux:menu.item wire:click="reRunImport" icon="arrow-path-rounded-square">Re-run import</flux:menu.item>

                                <flux:menu.separator />

                                <flux:menu.item wire:click="delete()" variant="danger" icon="trash">Delete</flux:menu.item>
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
                {{-- @endforeach --}}

                <flux:table.row>
                    <flux:table.cell variant="strong">#1</flux:table.cell>
                    <flux:table.cell>
                        <flux:badge color="zinc" icon="truck" size="sm">Suppliers</flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>
                        <flux:badge color="green" size="sm"><flux:icon.check-circle variant="micro" class="mr-2" /> Completed</flux:badge>
                    </flux:table.cell>
                    <flux:table.cell>Import iniziale contenuti</flux:table.cell>
                    <flux:table.cell>03-12-24</flux:table.cell>
                    <flux:table.cell class="flex justify-end">
                        <flux:dropdown>
                            <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                            <flux:menu>
                                <flux:menu.item wire:click="downloadXLSX" icon="arrow-down-tray">Download .XLSX</flux:menu.item>
                                <flux:menu.item wire:click="reRunImport" icon="arrow-path-rounded-square">Re-run import</flux:menu.item>

                                <flux:menu.separator />

                                <flux:menu.item wire:click="delete()" variant="danger" icon="trash">Delete</flux:menu.item>
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
