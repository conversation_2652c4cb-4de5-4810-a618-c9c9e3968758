<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <div class="flex justify-between">
        <div>
            <flux:heading size="xl" level="1">Order: {{ $order->code }}</flux:heading>
            <flux:subheading size="lg" class="mb-6">Here's the order info and details</flux:subheading>
        </div>
        <div class="flex gap-2">
            <div><flux:badge icon="hashtag" size="sm md:lg">{{ $order->order_code ?? "XX_XXX00-000000" }}</flux:badge></div>
            <div><flux:badge icon="calendar" size="sm md:lg">{{ $order->date->format('d-m-y') }}</flux:badge></div>
            <div><flux:badge icon="{{ $order->status->icon() }}" color="{{ $order->status->color() }}" size="sm md:lg">{{ $order->status->label() }}</flux:badge></div>
        </div>
    </div>

    {{-- Actions --}}
    <div class="flex flex-col my-4">
        <div class="flex justify-between gap-2">
            <div class="flex items-center">
            </div>
            <div>
                @can('view', $order)
                    <flux:button href="{{ route('orders.stats', $order->id) }}" wire:navigate size="sm">Stats</flux:button>
                @endcan
                @can('view', $order)
                    <flux:button wire:click="openCreator({{ $order->id }})" size="sm">Creator</flux:button>
                @endcan
                <flux:dropdown>
                    <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                    <flux:menu>
                        @can('update', $order)
                            <flux:menu.item wire:click="submit" icon="arrow-turn-down-right" :disabled="$order->status->value !== 'open'">Submit</flux:menu.item>
                            <flux:menu.item wire:click="accept" icon="check" :disabled="$order->status->value !== 'submitted'">Accept</flux:menu.item>
                            <flux:menu.item wire:click="reject" variant="danger" icon="x-mark" :disabled="$order->status->value !== 'submitted'">Reject</flux:menu.item>
                        @endcan

                        @can('view', $order)
                            <flux:menu.separator />
                            <flux:menu.item wire:click="downloadConfirmation" icon="clipboard-document-check" x-bind:disabled="! '{{ $order->confirmation_file }}'">Download confirmation</flux:menu.item>
                        @endcan

                        @can('update', $order)
                            <flux:menu.separator />
                            <flux:menu.item wire:click="edit({{ $order->id }})" icon="pencil-square">Edit</flux:menu.item>
                        @endcan
                        @can('delete', $order)
                            <flux:menu.separator />
                            <flux:menu.item wire:confirm="Are you sure you want to delete this order? This action cannot be undone." wire:click="delete({{ $order->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                        @endcan
                    </flux:menu>
                </flux:dropdown>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {{-- Clients and Partner --}}
        <flux:card size="sm" class="flex flex-col justify-between gap-4">
            <div>
                <flux:subheading class="mb-1">Clients details</flux:subheading>
                <flux:separator class="mb-2"></flux:separator>
            </div>
            <div>
                <flux:heading size="lg" class="mb-1 flex justify-between">
                    <span class="text-sm font-light">Client</span>
                    @can('view', $order->client)
                        <flux:link href="{{ route('clients.show', $order->client->id ?? '') }}" wire:navigate>{{ $order->client->company ?? '-' }}</flux:link>
                    @else
                        <span>{{ $order->client->company ?? '-' }}</span>
                    @endcan
                </flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between">
                    <span class="text-sm font-light">Partner</span>
                    @can('view', $order->partner)
                        <flux:link href="{{ route('partners.show', $order->partner->id ?? '') }}" wire:navigate>{{ $order->partner->company ?? '-' }}</flux:link>
                    @else
                        <span>{{ $order->partner->company ?? '-' }}</span>
                    @endcan
                </flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Int. Referent</span>{{ $order->internalReferent ? $order->internalReferent->first_name . ' ' . $order->internalReferent->last_name : '-' }}</flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Area Manager</span>{{ $order->areaManager ? $order->areaManager->first_name . ' ' . $order->areaManager->last_name : '-' }}</flux:heading>
            </div>
        </flux:card>

        {{-- Invoicing and Shipping --}}
        <flux:card size="sm" class="flex flex-col justify-between gap-4">
            <div>
                <div>
                    <flux:subheading class="mb-1">Invoicing address</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <flux:heading class="mt-1">{{ $order->invoicingAddress->company ?? '-' }}</flux:heading>
            </div>
            <div>
                <div>
                    <flux:subheading class="mb-1">Shipping address</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <flux:heading class="mt-1">{{ $order->shippingAddress->company ?? '-' }}</flux:heading>
            </div>
        </flux:card>

        {{-- Cart and Summary --}}
            <flux:card size="sm" class="flex flex-col justify-between">
                <div>
                    <flux:subheading class="mb-1">Cart details</flux:subheading>
                    <flux:separator class="mb-2"></flux:separator>
                </div>
                <div>
                    <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">Amount</span>{{ eu_currency($order->totalAmount) }}</flux:heading>
                    <flux:heading size="lg" class="mt-1 flex justify-between"><span class="text-sm font-light">With addons</span>{{ eu_currency($order->getTotalAmountAttribute($withAddons = true)) }}</flux:heading>
                </div>
                <div>
                    <flux:heading size="lg" class="mb-1 flex justify-end">{{ $order->paymentTerm ? $order->paymentTerm->name : '-' }}</flux:heading>
                </div>
            </flux:card>
    </div>

    {{-- Callout Messages --}}
    @if ($callout->show)
        <flux:callout variant="{{ $callout->variant }}" icon="{{ $callout->icon }}" heading="{{ $callout->heading }}" class="mt-4" />
    @endif

    <flux:separator variant="subtle" class="mt-8 mb-12" />

    {{-- Order Rows Listing --}}
    <livewire:components.inner-table.order-rows :resourceType="class_basename($order)" :resourceValue="$order" />

    <flux:separator variant="subtle" class="my-12" />

    {{-- Collaborators Listing --}}
    <livewire:components.inner-table.collaborators :resourceType="class_basename($order)" :resourceValue="$order" />

    <flux:separator variant="subtle" class="my-12" />

    {{-- Assets Listing --}}
    <livewire:components.inner-table.assets :resourceType="class_basename($order)" :resourceValue="$order" />

</flux:main>
