<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    {{-- Order Details --}}
    <div class="flex justify-between">
        <div>
            <flux:heading size="xl" level="1">
                Creator for order: {{ $order->code }}
                <flux:button wire:click="backToOrder({{ $order->id }})" size="xs" square>
                    <flux:icon name="arrow-uturn-left" variant="micro" />
                </flux:button>
            </flux:heading>
            <flux:subheading size="lg" class="mb-6">Here's you can add products to the order</flux:subheading>
        </div>
        <div class="flex gap-2">
            <div><flux:badge icon="hashtag" size="sm md:lg">{{ $order->order_code ?? "XX_XXX00-000000" }}</flux:badge></div>
            <div><flux:badge icon="calendar" size="sm md:lg">{{ $order->date->format('d-m-y') }}</flux:badge></div>
            <div><flux:badge icon="{{ $order->status->icon() }}" color="{{ $order->status->color() }}" size="sm md:lg">{{ $order->status->label() }}</flux:badge></div>
        </div>
    </div>

    {{-- Order Details --}}
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {{-- Clients and Partner --}}
        <flux:card size="sm" class="flex flex-col justify-between">
            <flux:subheading>Clients details</flux:subheading>
            <div>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Client</span> {{ $order->client->company ?? '-' }}</flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Partner</span> {{ $order->partner->company ?? '-' }}</flux:heading>
                <flux:heading size="lg" class="mb-1 flex justify-between"><span class="text-sm font-light">Int. Referent</span> {{ $order->internalReferent ? $order->internalReferent->first_name . ' ' . $order->internalReferent->last_name : '-' }}</flux:heading>
            </div>
        </flux:card>

        {{-- Invoicing and Shipping --}}
        <flux:card size="sm" class="flex flex-col justify-between">
            <div>
                <flux:subheading>Invoicing address</flux:subheading>
                <flux:heading class="mt-1">{{ $order->invoicingAddress->company ?? '-' }}</flux:heading>
            </div>
            <div>
                <flux:subheading>Shipping address</flux:subheading>
                <flux:heading class="mt-1">{{ $order->shippingAddress->company ?? '-' }}</flux:heading>
            </div>
        </flux:card>

        {{-- Cart and Summary --}}
        <livewire:components.orders.creator.summary-card :order="$order" />
    </div>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Search, Filters and Collections --}}
        <div class="flex flex-col gap-4">
            {{-- Serach --}}
            <div class="flex flex-col">
                <div class="flex justify-between gap-2">
                    {{-- <flux:button icon="rectangle-group" square /> --}}
                    <div class="grow">
                        <flux:input wire:model.live.debounce.1050ms="search" clearable placeholder="Priority applied by word..." />
                    </div>
                    @if ($this->areFiltersActive())
                    <div class="flex justify-end">
                        <flux:button wire:click="clearSearchAndFilters" icon="x-mark" square />
                    </div>
                    @endif
                </div>
            </div>

            {{-- Filters --}}
            <flux:accordion transition>
                <flux:accordion.item class="border-t border-gray-200">
                    <flux:accordion.heading class="px-1 pt-2 pb-1">
                        <flux:heading size="lg">Apply advanced filters</flux:heading>
                    </flux:accordion.heading>
                    <flux:accordion.content>
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <flux:field>
                                <flux:select searchable wire:model.live="selectedBrands" wire:click="fetchAvailableBrands"
                                    variant="listbox" size="sm" searchable multiple clearable
                                    placeholder="Choose brands...">
                                    @foreach ($availableBrands as $brand)
                                        <flux:select.option value="{{ $brand }}">{{ strtoupper($brand) }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                            </flux:field>
                            <flux:field>
                                <flux:select searchable wire:model.live="selectedTypes" wire:click="fetchAvailableTypes"
                                    variant="listbox" size="sm" searchable multiple clearable
                                    placeholder="Choose types...">
                                    @foreach ($availableTypes as $type)
                                        <flux:select.option value="{{ $type }}">{{ strtoupper($type) }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                            </flux:field>
                            <flux:field>
                                <flux:select searchable wire:model.live="selectedDestinationRooms" wire:click="fetchAvailableDestinationRooms"
                                    variant="listbox" size="sm" searchable multiple clearable
                                    placeholder="Choose destination rooms...">
                                    @foreach ($availableDestinationRooms as $destinationRoom)
                                        <flux:select.option value="{{ $destinationRoom }}">{{ strtoupper($destinationRoom) }}
                                        </flux:select.option>
                                    @endforeach
                                </flux:select>
                            </flux:field>
                            <flux:field>
                                <flux:select searchable wire:model.live="selectedStyles" wire:click="fetchAvailableStyles"
                                    variant="listbox" size="sm" searchable multiple clearable
                                    placeholder="Choose styles...">
                                    @foreach ($availableStyles as $style)
                                        <flux:select.option value="{{ $style }}">{{ strtoupper($style) }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                            </flux:field>
                            <flux:field>
                                <flux:select searchable wire:model.live="selectedMaterials" wire:click="fetchAvailableMaterials"
                                    variant="listbox" size="sm" searchable multiple clearable
                                    placeholder="Choose materials...">
                                    @foreach ($availableMaterials as $material)
                                        <flux:select.option value="{{ $material }}">{{ strtoupper($material) }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                            </flux:field>
                            <flux:field>
                                <flux:select searchable wire:model.live="selectedColors" wire:click="fetchAvailableColors"
                                    variant="listbox" size="sm" searchable multiple clearable
                                    placeholder="Choose colors...">
                                    @foreach ($availableColors as $color)
                                        <flux:select.option value="{{ $color }}">{{ strtoupper($color) }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                            </flux:field>
                        </div>
                    </flux:accordion.content>
                </flux:accordion.item>
            </flux:accordion>

            {{-- Collections --}}
            <flux:accordion wire:click="fetchAvailableCollections" transition>
                <flux:accordion.item class="border-t border-gray-200">
                    <flux:accordion.heading class="px-1 pt-2 pb-1">
                        <flux:heading size="lg">Select collections ({{ count($this->selectedCollections) }})</flux:heading>
                    </flux:accordion.heading>
                    <flux:accordion.content>
                        <flux:table :paginate="$this->collections" class="mb-8">
                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                                @foreach ($this->collections as $collection)
                                    <div wire:click="toggleCollection('{{ $collection->name }}')" wire:key="collection-{{ $collection->id }}" class="flex flex-col rounded-xl justify-between bg-white dark:bg-zinc-700 border border-zinc-200 dark:border-zinc-700">
                                        <div class="relative rounded-t-xl overflow-hidden">
                                            @if (in_array($collection->name, $this->selectedCollections))
                                                <div class="absolute top-0 left-0 m-2 p-2">
                                                    <flux:tooltip content="Selected">
                                                        <flux:icon.check-circle variant="solid" class="text-zinc-700 dark:text-zinc-700" />
                                                    </flux:tooltip>
                                                </div>
                                            @endif

                                            @if ($collection->image)
                                                <img src="{{ Storage::disk(config('filesystems.public'))->url($collection?->image) }}" class="w-full object-cover rounded-t-lg">
                                            @else
                                                <img src="{{ asset('no-image.jpg') }}" class="w-full object-cover rounded-t-lg">
                                            @endif
                                        </div>
                                        <div class="flex flex-col flex-auto justify-between bg-zinc-100 dark:bg-zinc-700 rounded-b-xl p-2">
                                            <flux:heading class="text-xs! mt-0!">{{ $collection->name }}</flux:heading>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </flux:table>
                    </flux:accordion.content>
                </flux:accordion.item>
            </flux:accordion>
        </div>

        {{-- Products Listing --}}
        <flux:table :paginate="$this->products" class="mb-8">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2">
                @forelse ($this->products as $product)
                    <livewire:components.orders.creator.product-card :product="$product" :client="$order->client" :key="$product->id" />
                @empty
                    <flux:subheading>No results found.</flux:subheading>
                @endforelse
            </div>
        </flux:table>
    </div>

    {{-- Modals --}}
    <livewire:components.products.modals.show />
    <livewire:components.orders.creator.modals.add-to-cart />
    <livewire:components.orders.creator.modals.cart :order="$order" />

</flux:main>
