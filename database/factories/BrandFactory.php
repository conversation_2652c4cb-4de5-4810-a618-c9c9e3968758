<?php

namespace Database\Factories;

use App\Enums\DeliveryTerms;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Brand>
 */
class BrandFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'prefix' => $this->faker->unique()->word,
            'name' => $this->faker->company,
            'price_range' => $this->faker->numberBetween(1, 5),
            'rating' => $this->faker->numberBetween(1, 3),
            'partnership_level' => $this->faker->numberBetween(1, 3),
            'lead_time' => $this->faker->word,
            'lead_time' => $this->faker->randomElement(\App\Enums\BrandLeadTime::cases()),
            'purchase_price_list' => 'NOIVA',
            'purchase_conditions' => $this->faker->sentence,
            'minimum_orderable' => $this->faker->numberBetween(10000, 100000),
            'extra_costs' => $this->faker->word,
            'delivery_terms' => $this->faker->optional()->randomElement(array_map(fn($case) => $case->value, DeliveryTerms::cases())),
            'yearly_bonus_info' => $this->faker->word,
            'catalogs' => $this->faker->url,
            'pricelist' => $this->faker->url,
            'valid_from' => $this->faker->date,
            'expected_pricelist_update' => $this->faker->date,
            'social_link' => $this->faker->url,
            'supplier_media_link' => $this->faker->url,
            'supplier_media_link_user' => $this->faker->userName,
            'supplier_media_link_password' => $this->faker->password,
            'image' => $this->faker->imageUrl,
            'notes' => $this->faker->paragraph,
        ];
    }
}
