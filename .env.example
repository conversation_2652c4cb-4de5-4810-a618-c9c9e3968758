APP_NAME=ErgoUp
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=Europe/Rome
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

APP_FEED_SIMPLE_PRODUCTS=https://pim.plytix.com/channels/67cb25b801069aeebe35538b/feed
APP_FEED_VARIANT_PRODUCTS=https://pim.plytix.com/channels/67cb25d62a641a0ee501be85/feed
APP_FEED_MODULAR_PRODUCTS=https://pim.plytix.com/channels/67cb2832c060f0a313744d41/feed
APP_FEED_MODULAR_OPTIONS=https://pim.plytix.com/channels/67cb284558b69f7a193d7264/feed

APP_FEED_PRODUCTS_TO_BE_DELETED=https://pim.plytix.com/channels/682aef39fae6897e83134b18/feed

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=bergomi
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

FILESYSTEM_DISK=local

FILESYSTEM_PRIVATE=local
FILESYSTEM_PUBLIC=public

DO_SPACES_KEY=
DO_SPACES_SECRET=
DO_SPACES_ENDPOINT="https://fra1.digitaloceanspaces.com"
DO_SPACES_REGION=fra1
DO_SPACES_BUCKET=bergomi-webapp
DO_SPACES_URL="https://bergomi-webapp.fra1.digitaloceanspaces.com"

SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=http://meilisearch:7700
MEILISEARCH_KEY=masterKey

BROADCAST_CONNECTION=log
QUEUE_CONNECTION=redis

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

VITE_APP_NAME="${APP_NAME}"
