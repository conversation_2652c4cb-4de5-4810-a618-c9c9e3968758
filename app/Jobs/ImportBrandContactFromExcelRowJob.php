<?php

namespace App\Jobs;

use App\Models\Brand;
use App\Models\Contact;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportBrandContactFromExcelRowJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $email = trim($this->row['email'] ?? '');

            if (empty($email)) {
                Log::warning("Skipping brand contact row with empty email", ['row' => $this->row]);
                return;
            }

            Log::info("Processing brand contact import for email: {$email}");

            // Upsert contact
            Contact::upsert([
                'name' => $this->row['name'] ?? '',
                'departments' => $this->getValidDepartments($this->row['department'] ?? null),
                'position' => $this->row['position'] ?? null,
                'email' => $email,
                'phone' => $this->row['phone'] ?? null,
                'country' => $this->getValidCountry($this->row['country'] ?? null),
                'languages' => $this->getValidLanguages($this->row['languages'] ?? null),
                'notes' => $this->row['notes'] ?? null,
            ], ['email']);

            // Get the contact
            $contact = Contact::where('email', $email)->first();

            // Attach to brand if specified
            $brandPrefix = trim($this->row['brand_prefix'] ?? '');
            if (!empty($brandPrefix) && $contact) {
                $brand = Brand::where('prefix', $brandPrefix)->first();

                if ($brand) {
                    if (!$contact->brands()->where('brand_id', $brand->id)->exists()) {
                        $contact->brands()->attach($brand);
                        Log::info("Attached contact {$email} to brand {$brandPrefix}");
                    }
                } else {
                    Log::warning("Brand not found for prefix: {$brandPrefix}", ['contact_email' => $email]);
                }
            }

            Log::info("Successfully imported brand contact: {$email}");

        } catch (\Exception $e) {
            Log::error("Failed to import brand contact from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get valid departments
     */
    private function getValidDepartments(?string $departments): ?string
    {
        if (empty($departments)) {
            return null;
        }

        $validDepartments = array_map(fn($dept) => $dept->value, \App\Enums\ContactPositions::cases());
        $departmentsArray = array_filter(
            array_map('trim', explode(',', strtolower($departments))),
            fn($department) => in_array($department, $validDepartments)
        );

        return empty($departmentsArray) ? null : json_encode($departmentsArray);
    }

    /**
     * Get valid country
     */
    private function getValidCountry(?string $country): ?string
    {
        if (empty($country)) {
            return null;
        }

        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        return in_array(strtoupper($country), $validCountries) ? strtoupper($country) : null;
    }

    /**
     * Get valid languages
     */
    private function getValidLanguages(?string $languages): ?string
    {
        if (empty($languages)) {
            return null;
        }

        $validLanguages = array_map(fn($lang) => $lang->value, \PrinsFrank\Standards\Language\LanguageAlpha2::cases());
        $languagesArray = array_filter(
            array_map('trim', explode(',', strtolower($languages))),
            fn($language) => in_array($language, $validLanguages)
        );

        return empty($languagesArray) ? null : json_encode($languagesArray);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportBrandContactFromExcelRowJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
