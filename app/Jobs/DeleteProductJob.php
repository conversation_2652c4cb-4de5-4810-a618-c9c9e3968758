<?php

namespace App\Jobs;

use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DeleteProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * The SKU of the product to delete.
     *
     * @var string
     */
    public $sku;

    /**
     * Create a new job instance.
     */
    public function __construct(string $sku)
    {
        $this->sku = $sku;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Find and delete the product if it exists
            $product = Product::where('sku', $this->sku)->first();
            
            if ($product) {
                $product->delete();
                Log::info("Product deleted successfully", ['sku' => $this->sku]);
            } else {
                Log::warning("Product not found for deletion", ['sku' => $this->sku]);
            }
        } catch (\Exception $e) {
            Log::error("Failed to delete product", [
                'sku' => $this->sku,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Re-throw the exception to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("DeleteProductJob failed after all retries", [
            'sku' => $this->sku,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
