<?php

namespace App\Jobs;

use App\Models\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportCollectionJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $code = trim($this->row['code'] ?? '');

            if (empty($code)) {
                Log::warning("Skipping collection row with empty code", ['row' => $this->row]);
                return;
            }

            Log::info("Processing collection import for code: {$code}");

            Collection::upsert([
                'code' => $code,
                'name' => $this->row['name'] ?? null,
                'image' => isset($this->row['image']) && $this->row['image'] ? 'collections/' . $this->row['image'] : null,
            ], ['code']);

            Log::info("Successfully imported collection: {$code}");

        } catch (\Exception $e) {
            Log::error("Failed to import collection from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportCollectionFromExcelRowJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
