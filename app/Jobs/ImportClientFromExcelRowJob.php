<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Client;
use App\Models\Partner;
use App\Models\PaymentTerm;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportClientFromExcelRowJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $email = trim($this->row['email'] ?? '');

            if (empty($email)) {
                Log::warning("Skipping client row with empty email", ['row' => $this->row]);
                return;
            }

            Log::info("Processing client import for email: {$email}");

            Client::create([
                'type' => $this->getValidClientType($this->row['type'] ?? null),
                'partner_id' => $this->getPartnerId($this->row['partner_id'] ?? null),
                'internal_referent_id' => $this->getInternalReferentId($this->row['internal_referent_email'] ?? null),
                'company' => $this->row['company'] ?? null,
                'email' => $email,
                'country' => $this->getValidCountry($this->row['country'] ?? null),
                'payment_term_id' => $this->getPaymentTermId($this->row['payment_term_code'] ?? null),
                'commercial_category' => $this->getValidCommercialCategory($this->row['commercial_category'] ?? null),
                'priority' => $this->getValidPriority($this->row['priority'] ?? null),
                'minimum_orderable' => isset($this->row['minimum_orderable']) && $this->row['minimum_orderable'] ? ($this->row['minimum_orderable'] * 100) : 0,
                'handling_and_packing' => $this->row['handling_and_packing'] ?? null,
                'delivery_terms' => $this->getValidDeliveryTerm($this->row['delivery_terms'] ?? null),
                'countries_of_expertise' => $this->getValidCountriesOfExpertise($this->row['countries_of_expertise'] ?? null),
                'notes' => $this->row['notes'] ?? null,
            ]);

            Log::info("Successfully imported client: {$email}");

        } catch (\Exception $e) {
            Log::error("Failed to import client from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get valid client type
     */
    private function getValidClientType(?string $type): string
    {
        if (empty($type)) {
            return \App\Enums\ClientTypes::Lead->value;
        }

        $validTypes = array_map(fn($type) => $type->value, \App\Enums\ClientTypes::cases());
        return in_array(strtolower($type), $validTypes) ? strtolower($type) : \App\Enums\ClientTypes::Lead->value;
    }

    /**
     * Get partner ID
     */
    private function getPartnerId(?string $partnerId): ?int
    {
        if (empty($partnerId)) {
            return null;
        }

        try {
            return Partner::findOrFail($partnerId)->id;
        } catch (\Exception) {
            Log::warning("Partner not found: {$partnerId}");
            return null;
        }
    }

    /**
     * Get internal referent ID
     */
    private function getInternalReferentId(?string $email): ?int
    {
        if (empty($email)) {
            return null;
        }

        return User::where('email', $email)->value('id');
    }

    /**
     * Get valid country
     */
    private function getValidCountry(?string $country): ?string
    {
        if (empty($country)) {
            return null;
        }

        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        return in_array(strtoupper($country), $validCountries) ? strtoupper($country) : null;
    }

    /**
     * Get payment term ID
     */
    private function getPaymentTermId(?string $code): ?int
    {
        if (empty($code)) {
            return null;
        }

        return PaymentTerm::where('code', $code)->value('id');
    }

    /**
     * Get valid commercial category
     */
    private function getValidCommercialCategory(?string $category): ?string
    {
        if (empty($category)) {
            return null;
        }

        $validCategories = array_map(fn($category) => $category->value, \App\Enums\CommercialCategories::cases());
        return in_array(strtolower($category), $validCategories) ? strtolower($category) : null;
    }

    /**
     * Get valid priority
     */
    private function getValidPriority(?string $priority): ?string
    {
        if (empty($priority)) {
            return null;
        }

        $validPriorities = array_map(fn($priority) => $priority->value, \App\Enums\ClientPartnerPriorities::cases());
        return in_array(strtolower($priority), $validPriorities) ? strtolower($priority) : null;
    }

    /**
     * Get valid delivery term
     */
    private function getValidDeliveryTerm(?string $term): ?string
    {
        if (empty($term)) {
            return null;
        }

        $validTerms = array_map(fn($term) => $term->value, \App\Enums\DeliveryTerms::cases());
        return in_array(strtolower($term), $validTerms) ? strtolower($term) : null;
    }

    /**
     * Get valid countries of expertise
     */
    private function getValidCountriesOfExpertise(?string $countries): ?string
    {
        if (empty($countries)) {
            return null;
        }

        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        $countriesArray = array_filter(
            array_map('trim', explode(',', strtoupper($countries))),
            fn($country) => in_array($country, $validCountries)
        );

        return empty($countriesArray) ? null : json_encode($countriesArray);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportClientFromExcelRowJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
