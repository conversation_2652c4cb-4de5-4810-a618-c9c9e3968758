<?php

namespace App\Jobs;

use App\Models\Brand;
use App\Models\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportClientBrandFromExcelRowJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $clientId = trim($this->row['client_id'] ?? '');
            $brandPrefix = trim($this->row['brand_prefix'] ?? '');

            if (empty($clientId) || empty($brandPrefix)) {
                Log::warning("Skipping client brand row with empty client_id or brand_prefix", ['row' => $this->row]);
                return;
            }

            Log::info("Processing client brand import for client_id: {$clientId}, brand_prefix: {$brandPrefix}");

            $client = Client::find($clientId);
            $brand = Brand::where('prefix', $brandPrefix)->first();

            if (!$client) {
                Log::warning("Client not found for ID: {$clientId}");
                return;
            }

            if (!$brand) {
                Log::warning("Brand not found for prefix: {$brandPrefix}");
                return;
            }

            // Check if relationship already exists
            $existingBrand = $client->brands()->where('brand_id', $brand->id)->first();
            if (!$existingBrand) {
                $client->brands()->attach($brand);
                Log::info("Successfully attached brand {$brandPrefix} to client {$clientId}");
            } else {
                Log::info("Brand {$brandPrefix} already attached to client {$clientId}");
            }

        } catch (\Exception $e) {
            Log::error("Failed to import client brand from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportClientBrandFromExcelRowJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
