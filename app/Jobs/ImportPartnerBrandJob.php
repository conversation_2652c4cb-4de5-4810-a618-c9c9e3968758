<?php

namespace App\Jobs;

use App\Models\Brand;
use App\Models\Partner;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportPartnerBrandJob implements ShouldQueue
{
    use Queueable;

    public function __construct(public array $row) {}

    public function handle(): void
    {
        try {
            $partnerId = trim($this->row['partner_id'] ?? '');
            $brandPrefix = trim($this->row['brand_prefix'] ?? '');

            if (empty($partnerId) || empty($brandPrefix)) {
                Log::warning("Skipping partner brand row with empty partner_id or brand_prefix", ['row' => $this->row]);
                return;
            }

            $partner = Partner::find($partnerId);
            $brand = Brand::where('prefix', $brandPrefix)->first();

            if (!$partner || !$brand) {
                Log::warning("Partner or Brand not found", ['partner_id' => $partnerId, 'brand_prefix' => $brandPrefix]);
                return;
            }

            if (!$partner->brands()->where('brand_id', $brand->id)->exists()) {
                $partner->brands()->attach($brand);
                Log::info("Successfully attached brand {$brandPrefix} to partner {$partnerId}");
            }

        } catch (\Exception $e) {
            Log::error("Failed to import partner brand", ['row' => $this->row, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("ImportPartnerBrandJob failed", ['row' => $this->row, 'error' => $exception->getMessage()]);
    }
}
