<?php

namespace App\Jobs;

use App\Models\Brand;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncBrandTagsFromProductsJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Brand $brand,
        public bool $force = false
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting tag sync for brand: {$this->brand->name} (ID: {$this->brand->id})");

            $result = $this->syncBrandTags();

            if ($result) {
                Log::info("Successfully synced tags for brand: {$this->brand->name} (ID: {$this->brand->id})");
            } else {
                Log::info("Skipped tag sync for brand: {$this->brand->name} (ID: {$this->brand->id}) - already has tags or no tags to sync");
            }
        } catch (\Exception $e) {
            Log::error("Failed to sync tags for brand: {$this->brand->name} (ID: {$this->brand->id})", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Sync tags for the brand from its products
     */
    private function syncBrandTags(): bool
    {
        // Load products with tags
        $this->brand->load(['products.tags']);

        // Check if brand already has tags and force is not enabled
        if (!$this->force && $this->brand->tags()->whereIn('type', ['type', 'material', 'destination_room', 'style'])->exists()) {
            return false; // Skip if brand already has tags
        }

        // Collect all unique values for each type
        $typeValues = collect();
        $materialTags = collect();
        $destinationRoomTags = collect();
        $styleTags = collect();

        foreach ($this->brand->products as $product) {
            // For 'type' - get from product field (not tags)
            if (!empty($product->type)) {
                $typeValues->push($product->type);
            }

            // For the other types - get from tags
            $materialTags = $materialTags->merge($product->tags->where('type', 'material'));
            $destinationRoomTags = $destinationRoomTags->merge($product->tags->where('type', 'destination_room'));
            $styleTags = $styleTags->merge($product->tags->where('type', 'style'));
        }

        // Remove existing tags of these types if force is enabled
        if ($this->force) {
            $this->brand->tags()->whereIn('type', ['type', 'material', 'destination_room', 'style'])->detach();
        }

        $tagsCreated = 0;

        // Create/attach 'type' tags from product type field
        $uniqueTypes = $typeValues->unique()->filter();
        foreach ($uniqueTypes as $typeValue) {
            $tag = $this->brand->attachTag($typeValue, 'type');
            $tagsCreated++;
        }

        // Attach existing material tags
        $uniqueMaterialTags = $materialTags->unique('id');
        foreach ($uniqueMaterialTags as $tag) {
            if (!$this->brand->tags()->where('tags.id', $tag->id)->exists()) {
                $this->brand->attachTag($tag);
                $tagsCreated++;
            }
        }

        // Attach existing destination_room tags
        $uniqueDestinationRoomTags = $destinationRoomTags->unique('id');
        foreach ($uniqueDestinationRoomTags as $tag) {
            if (!$this->brand->tags()->where('tags.id', $tag->id)->exists()) {
                $this->brand->attachTag($tag);
                $tagsCreated++;
            }
        }

        // Attach existing style tags
        $uniqueStyleTags = $styleTags->unique('id');
        foreach ($uniqueStyleTags as $tag) {
            if (!$this->brand->tags()->where('tags.id', $tag->id)->exists()) {
                $this->brand->attachTag($tag);
                $tagsCreated++;
            }
        }

        return $tagsCreated > 0;
    }
}
