<?php

namespace App\Jobs;

use App\Models\Brand;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncBrandTagsFromProductsJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Brand $brand,
        public bool $force = false
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting tag sync for brand: {$this->brand->name} (ID: {$this->brand->id})");

            $result = $this->syncBrandTags();

            if ($result) {
                Log::info("Successfully synced tags for brand: {$this->brand->name} (ID: {$this->brand->id})");
            } else {
                Log::info("Skipped tag sync for brand: {$this->brand->name} (ID: {$this->brand->id}) - already has tags or no tags to sync");
            }
        } catch (\Exception $e) {
            Log::error("Failed to sync tags for brand: {$this->brand->name} (ID: {$this->brand->id})", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Sync tags for the brand from its products
     */
    private function syncBrandTags(): bool
    {
        // Load products with tags
        $this->brand->load(['products.tags']);

        // Check if brand already has tags and force is not enabled
        if (!$this->force && $this->brand->tags()->whereIn('type', ['type', 'material', 'destination_room', 'style'])->exists()) {
            return false; // Skip if brand already has tags
        }

        // Get all unique tags from products for the 4 specific types
        $tagTypes = ['type', 'material', 'destination_room', 'style'];
        $allProductTags = collect();

        foreach ($this->brand->products as $product) {
            foreach ($tagTypes as $tagType) {
                $productTags = $product->tags->where('type', $tagType);
                $allProductTags = $allProductTags->merge($productTags);
            }
        }

        // Remove duplicates based on name and type
        $uniqueTags = $allProductTags->unique(function ($tag) {
            return $tag->type . '|' . $tag->name;
        });

        if ($uniqueTags->isEmpty()) {
            return false; // No tags to sync
        }

        // Remove existing tags of these types if force is enabled
        if ($this->force) {
            $this->brand->tags()->whereIn('type', $tagTypes)->detach();
        }

        // Attach the unique tags to the brand
        foreach ($uniqueTags as $tag) {
            // Check if brand already has this specific tag
            if (!$this->brand->tags()->where('tags.id', $tag->id)->exists()) {
                $this->brand->attachTag($tag);
            }
        }

        return true;
    }
}
