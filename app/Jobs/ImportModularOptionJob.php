<?php

namespace App\Jobs;

use App\Models\Option;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportModularOptionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * The option row data.
     *
     * @var array
     */
    public $row;

    /**
     * Create a new job instance.
     */
    public function __construct(array $row)
    {
        $this->row = $row;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Normalize empty values to null
            $row = collect($this->row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

            // Upsert the option
            Option::upsert([
                'sku' => $row['sku'],
                'code' => $row['code'] ?? null,
                'description' => $row['description'],
                'category' => $row['category'] ?? null,
                'price_category' => $row['price_category'] ?? null,
            ], ['sku']);

            // Get the option
            $option = Option::where('sku', $row['sku'])->first();

            // Handle image download
            if (!empty($row['image'])) {
                try {
                    $response = Http::timeout(5)->get($row['image']);
                    if ($response->successful()) {
                        $extension = pathinfo(parse_url($row['image'], PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
                        $fileName = 'options/' . $row['sku'] . '.' . $extension;
                        Storage::disk(config('filesystems.public'))->put($fileName, $response->body());
                        $option->image = $fileName;
                        $option->save();
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to download image for modular option", [
                        'sku' => $row['sku'],
                        'image_url' => $row['image'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("Modular option imported successfully", ['sku' => $row['sku']]);

        } catch (\Exception $e) {
            Log::error("Failed to import modular option", [
                'sku' => $this->row['sku'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportModularOptionJob failed after all retries", [
            'sku' => $this->row['sku'] ?? 'unknown',
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
