<?php

namespace App\Jobs\Import\Relations;

use App\Models\Address;
use App\Models\Partner;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportPartnerAddressJob implements ShouldQueue
{
    use Queueable;

    public function __construct(public array $row) {}

    public function handle(): void
    {
        try {
            $partnerId = trim($this->row['partner_id'] ?? '');

            if (empty($partnerId)) {
                Log::warning("Skipping partner address row with empty partner_id", ['row' => $this->row]);
                return;
            }

            $partner = Partner::find($partnerId);
            if (!$partner) {
                Log::warning("Partner not found for ID: {$partnerId}");
                return;
            }

            Address::create([
                'addressable_id' => $partner->id,
                'addressable_type' => Partner::class,
                'name' => $this->row['name'] ?? null,
                'type' => $this->getValidAddressType($this->row['type'] ?? null),
                'parent_id' => $this->row['parent_id'] ?? null,
                'code_invoicing' => $this->row['code_invoicing'] ?? null,
                'code_shipping' => $this->row['code_shipping'] ?? null,
                'company' => $this->row['company'] ?? null,
                'vat_number' => $this->row['vat_number'] ?? null,
                'fiscal_code' => $this->row['fiscal_code'] ?? null,
                'sdi_code' => $this->row['sdi_code'] ?? null,
                'street' => $this->row['street'] ?? null,
                'city' => $this->row['city'] ?? null,
                'zip' => $this->row['zip'] ?? null,
                'state' => $this->row['state'] ?? null,
                'country' => $this->getValidCountry($this->row['country'] ?? null),
            ]);

            Log::info("Successfully imported partner address for partner_id: {$partnerId}");

        } catch (\Exception $e) {
            Log::error("Failed to import partner address", ['row' => $this->row, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    private function getValidAddressType(?string $type): string
    {
        if (empty($type)) return \App\Enums\AddressTypes::Invoicing->value;
        $validTypes = array_map(fn($type) => $type->value, \App\Enums\AddressTypes::cases());
        return in_array(strtolower($type), $validTypes) ? strtolower($type) : \App\Enums\AddressTypes::Invoicing->value;
    }

    private function getValidCountry(?string $country): ?string
    {
        if (empty($country)) return null;
        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        return in_array(strtoupper($country), $validCountries) ? strtoupper($country) : null;
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("ImportPartnerAddressJob failed", ['row' => $this->row, 'error' => $exception->getMessage()]);
    }
}
