<?php

namespace App\Jobs\Import\Relations;

use App\Models\Contact;
use App\Models\Partner;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportPartnerContactJob implements ShouldQueue
{
    use Queueable;

    public function __construct(public array $row) {}

    public function handle(): void
    {
        try {
            $email = trim($this->row['email'] ?? '');
            $partnerId = trim($this->row['partner_id'] ?? '');

            if (empty($email) || empty($partnerId)) {
                Log::warning("Skipping partner contact row with empty email or partner_id", ['row' => $this->row]);
                return;
            }

            Contact::upsert([
                'name' => $this->row['name'] ?? '',
                'departments' => $this->getValidDepartments($this->row['department'] ?? null),
                'position' => $this->row['position'] ?? null,
                'email' => $email,
                'phone' => $this->row['phone'] ?? null,
                'country' => $this->getValidCountry($this->row['country'] ?? null),
                'languages' => $this->getValidLanguages($this->row['languages'] ?? null),
                'notes' => $this->row['notes'] ?? null,
            ], ['email']);

            $contact = Contact::where('email', $email)->first();
            $partner = Partner::find($partnerId);

            if ($contact && $partner && !$contact->partners()->where('partner_id', $partner->id)->exists()) {
                $contact->partners()->attach($partner);
                Log::info("Attached contact {$email} to partner {$partnerId}");
            }

        } catch (\Exception $e) {
            Log::error("Failed to import partner contact", ['row' => $this->row, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    private function getValidDepartments(?string $departments): ?string
    {
        if (empty($departments)) return null;
        $validDepartments = array_map(fn($dept) => $dept->value, \App\Enums\ContactPositions::cases());
        $departmentsArray = array_filter(array_map('trim', explode(',', strtolower($departments))), fn($department) => in_array($department, $validDepartments));
        return empty($departmentsArray) ? null : json_encode($departmentsArray);
    }

    private function getValidCountry(?string $country): ?string
    {
        if (empty($country)) return null;
        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        return in_array(strtoupper($country), $validCountries) ? strtoupper($country) : null;
    }

    private function getValidLanguages(?string $languages): ?string
    {
        if (empty($languages)) return null;
        $validLanguages = array_map(fn($lang) => $lang->value, \PrinsFrank\Standards\Language\LanguageAlpha2::cases());
        $languagesArray = array_filter(array_map('trim', explode(',', strtolower($languages))), fn($language) => in_array($language, $validLanguages));
        return empty($languagesArray) ? null : json_encode($languagesArray);
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("ImportPartnerContactJob failed", ['row' => $this->row, 'error' => $exception->getMessage()]);
    }
}
