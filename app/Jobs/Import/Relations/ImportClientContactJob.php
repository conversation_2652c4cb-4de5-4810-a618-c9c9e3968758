<?php

namespace App\Jobs\Import\Relations;

use App\Models\Client;
use App\Models\Contact;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportClientContactJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $email = trim($this->row['email'] ?? '');
            $clientId = trim($this->row['client_id'] ?? '');

            if (empty($email) || empty($clientId)) {
                Log::warning("Skipping client contact row with empty email or client_id", ['row' => $this->row]);
                return;
            }

            Log::info("Processing client contact import for email: {$email}, client_id: {$clientId}");

            // Upsert contact
            Contact::upsert([
                'name' => $this->row['name'] ?? '',
                'departments' => $this->getValidDepartments($this->row['department'] ?? null),
                'position' => $this->row['position'] ?? null,
                'email' => $email,
                'phone' => $this->row['phone'] ?? null,
                'country' => $this->getValidCountry($this->row['country'] ?? null),
                'languages' => $this->getValidLanguages($this->row['languages'] ?? null),
                'notes' => $this->row['notes'] ?? null,
            ], ['email']);

            // Get the contact and client
            $contact = Contact::where('email', $email)->first();
            $client = Client::find($clientId);

            if (!$client) {
                Log::warning("Client not found for ID: {$clientId}");
                return;
            }

            if ($contact) {
                if (!$contact->clients()->where('client_id', $client->id)->exists()) {
                    $contact->clients()->attach($client);
                    Log::info("Attached contact {$email} to client {$clientId}");
                }
            }

            Log::info("Successfully imported client contact: {$email}");

        } catch (\Exception $e) {
            Log::error("Failed to import client contact from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get valid departments
     */
    private function getValidDepartments(?string $departments): ?string
    {
        if (empty($departments)) {
            return null;
        }

        $validDepartments = array_map(fn($dept) => $dept->value, \App\Enums\ContactPositions::cases());
        $departmentsArray = array_filter(
            array_map('trim', explode(',', strtolower($departments))),
            fn($department) => in_array($department, $validDepartments)
        );

        return empty($departmentsArray) ? null : json_encode($departmentsArray);
    }

    /**
     * Get valid country
     */
    private function getValidCountry(?string $country): ?string
    {
        if (empty($country)) {
            return null;
        }

        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        return in_array(strtoupper($country), $validCountries) ? strtoupper($country) : null;
    }

    /**
     * Get valid languages
     */
    private function getValidLanguages(?string $languages): ?string
    {
        if (empty($languages)) {
            return null;
        }

        $validLanguages = array_map(fn($lang) => $lang->value, \PrinsFrank\Standards\Language\LanguageAlpha2::cases());
        $languagesArray = array_filter(
            array_map('trim', explode(',', strtolower($languages))),
            fn($language) => in_array($language, $validLanguages)
        );

        return empty($languagesArray) ? null : json_encode($languagesArray);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportClientContactJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
