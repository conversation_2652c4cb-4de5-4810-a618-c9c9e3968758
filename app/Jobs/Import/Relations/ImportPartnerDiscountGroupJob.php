<?php

namespace App\Jobs\Import\Relations;

use App\Models\Partner;
use App\Models\DiscountGroup;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportPartnerDiscountGroupJob implements ShouldQueue
{
    use Queueable;

    public function __construct(public array $row) {}

    public function handle(): void
    {
        try {
            $partnerId = trim($this->row['partner_id'] ?? '');
            $discountGroupCode = trim($this->row['discount_group_code'] ?? '');

            if (empty($partnerId) || empty($discountGroupCode)) {
                Log::warning("Skipping partner discount group row with empty partner_id or discount_group_code", ['row' => $this->row]);
                return;
            }

            $partner = Partner::find($partnerId);
            $discountGroup = DiscountGroup::where('code', $discountGroupCode)->first();

            if (!$partner || !$discountGroup) {
                Log::warning("Partner or DiscountGroup not found", ['partner_id' => $partnerId, 'discount_group_code' => $discountGroupCode]);
                return;
            }

            $pivotData = [
                'discount' => $this->row['discount'] ?? 0,
                'extra_discount_threshold' => $this->row['extra_discount_threshold'] ?? null,
                'extra_discount' => $this->row['extra_discount'] ?? null,
            ];

            $existingDiscountGroup = $partner->discountGroups()->where('code', $discountGroupCode)->first();

            if ($existingDiscountGroup) {
                $partner->discountGroups()->updateExistingPivot($discountGroup->id, $pivotData);
                Log::info("Updated discount group {$discountGroupCode} for partner {$partnerId}");
            } else {
                $partner->discountGroups()->attach($discountGroup, $pivotData);
                Log::info("Attached discount group {$discountGroupCode} to partner {$partnerId}");
            }

        } catch (\Exception $e) {
            Log::error("Failed to import partner discount group", ['row' => $this->row, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("ImportPartnerDiscountGroupJob failed", ['row' => $this->row, 'error' => $exception->getMessage()]);
    }
}
