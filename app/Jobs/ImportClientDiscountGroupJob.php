<?php

namespace App\Jobs;

use App\Models\Client;
use App\Models\DiscountGroup;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportClientDiscountGroupJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $clientId = trim($this->row['client_id'] ?? '');
            $discountGroupCode = trim($this->row['discount_group_code'] ?? '');

            if (empty($clientId) || empty($discountGroupCode)) {
                Log::warning("Skipping client discount group row with empty client_id or discount_group_code", ['row' => $this->row]);
                return;
            }

            Log::info("Processing client discount group import for client_id: {$clientId}, discount_group_code: {$discountGroupCode}");

            $client = Client::find($clientId);
            $discountGroup = DiscountGroup::where('code', $discountGroupCode)->first();

            if (!$client) {
                Log::warning("Client not found for ID: {$clientId}");
                return;
            }

            if (!$discountGroup) {
                Log::warning("Discount group not found for code: {$discountGroupCode}");
                return;
            }

            // Check if relationship already exists
            $existingDiscountGroup = $client->discountGroups()->where('code', $discountGroupCode)->first();

            $pivotData = [
                'discount' => $this->row['discount'] ?? 0,
                'extra_discount_threshold' => $this->row['extra_discount_threshold'] ?? null,
                'extra_discount' => $this->row['extra_discount'] ?? null,
            ];

            if ($existingDiscountGroup) {
                $client->discountGroups()->updateExistingPivot($discountGroup->id, $pivotData);
                Log::info("Updated discount group {$discountGroupCode} for client {$clientId}");
            } else {
                $client->discountGroups()->attach($discountGroup, $pivotData);
                Log::info("Attached discount group {$discountGroupCode} to client {$clientId}");
            }

        } catch (\Exception $e) {
            Log::error("Failed to import client discount group from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportClientDiscountGroupFromExcelRowJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
