<?php

namespace App\Jobs;

use App\Models\Address;
use App\Models\Supplier;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportSupplierAddressJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $row
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $supplierCode = trim($this->row['supplier_code'] ?? '');

            if (empty($supplierCode)) {
                Log::warning("Skipping supplier address row with empty supplier_code", ['row' => $this->row]);
                return;
            }

            Log::info("Processing supplier address import for supplier_code: {$supplierCode}");

            $supplier = Supplier::where('code', $supplierCode)->first();

            if (!$supplier) {
                Log::warning("Supplier not found for code: {$supplierCode}");
                return;
            }

            Address::create([
                'addressable_id' => $supplier->id,
                'addressable_type' => Supplier::class,
                'name' => $this->row['name'] ?? null,
                'type' => $this->getValidAddressType($this->row['type'] ?? null),
                'parent_id' => $this->row['parent_id'] ?? null,
                'code_invoicing' => $this->row['code_invoicing'] ?? null,
                'code_shipping' => $this->row['code_shipping'] ?? null,
                'company' => $this->row['company'] ?? null,
                'vat_number' => $this->row['vat_number'] ?? null,
                'fiscal_code' => $this->row['fiscal_code'] ?? null,
                'sdi_code' => $this->row['sdi_code'] ?? null,
                'street' => $this->row['street'] ?? null,
                'city' => $this->row['city'] ?? null,
                'zip' => $this->row['zip'] ?? null,
                'state' => $this->row['state'] ?? null,
                'country' => $this->getValidCountry($this->row['country'] ?? null),
            ]);

            Log::info("Successfully imported supplier address for supplier_code: {$supplierCode}");

        } catch (\Exception $e) {
            Log::error("Failed to import supplier address from Excel row", [
                'row' => $this->row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get valid address type
     */
    private function getValidAddressType(?string $type): string
    {
        if (empty($type)) {
            return \App\Enums\AddressTypes::Invoicing->value;
        }

        $validTypes = array_map(fn($type) => $type->value, \App\Enums\AddressTypes::cases());
        return in_array(strtolower($type), $validTypes) ? strtolower($type) : \App\Enums\AddressTypes::Invoicing->value;
    }

    /**
     * Get valid country
     */
    private function getValidCountry(?string $country): ?string
    {
        if (empty($country)) {
            return null;
        }

        $validCountries = array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases());
        return in_array(strtoupper($country), $validCountries) ? strtoupper($country) : null;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportSupplierAddressJob failed after all retries", [
            'row' => $this->row,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
