<?php

namespace App\Jobs;

use App\Models\CustomProduct;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use App\Models\{Brand, Collection, DiscountGroup, Product};
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ImportSimpleProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * The product row data.
     *
     * @var array
     */
    public $row;

    /**
     * Create a new job instance.
     */
    public function __construct(array $row)
    {
        $this->row = $row;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Clear all the empty column to be NULL
            $row = collect($this->row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

            // Get the brand by prefix
            $brand = Brand::where('prefix', $row['brand_prefix'])->first();

            // Get the collection by code or create it
            $collection = (!empty($row['collection_code']) && !empty($row['collection_name']))
                ? Collection::firstOrCreate(['code' => $row['collection_code']], ['name' => $row['collection_name']])
                : null;

            // Get the discount group by code
            $discountGroup = !empty($row['discount_group_code'])
                ? DiscountGroup::where('code', $row['discount_group_code'])->first()
                : null;

            // Check if the product is soft deleted and if so, restore it
            if (Product::onlyTrashed()->where('sku', $row['sku'])->exists()) {
                Product::onlyTrashed()->where('sku', $row['sku'])->restore();
            }

            // Upsert the product
            Product::upsert([
                'brand_id' => $brand->id ?? null,
                'collection_id' => $collection->id ?? null,
                'discount_group_id' => $discountGroup->id ?? null,

                'sku' => $row['sku'],
                'adhoc_sku' => $row['adhoc_sku'] ?? null,
                'adhoc_discount_category' => $row['adhoc_discount_category'] ?? null,
                'ean_code' => $row['ean_code'] ?? null,

                'description' => $row['description'],
                'extra_description' => $row['extra_description'] ?? null,
                'dimensions' => $row['dimensions'] ?? null,

                'selling_price' => $row['selling_price'] ? ($row['selling_price'] * 100) : null,
                'purchasing_price' => $row['purchasing_price'] ? ($row['purchasing_price'] * 100) : null,

                'purchase_units' => $row['purchase_units'] ?? null,
                'leadtime' => $row['leadtime'] ?? null,
                'type' => $row['type'] ?? null,
                'country_of_origin' => $row['country_of_origin'] ?? null,
                'supplier_color' => $row['supplier_color'] ?? null,
                'hs_code' => $row['hs_code'] ?? null,

                'height' => $row['height'] ?? null,
                'length' => $row['length'] ?? null,
                'width' => $row['width'] ?? null,
                'diameter' => $row['diameter'] ?? null,
                'volume' => $row['volume'] ?? null,
                'capacity' => $row['capacity'] ?? null,
                'net_weight' => $row['net_weight'] ?? null,
            ], ['sku']);

            // Get the product
            $product = Product::where('sku', $row['sku'])->first();

            if (!empty($row['image'])) {
                try {
                    $response = Http::timeout(5)->get($row['image']);
                    if ($response->successful()) {
                        $extension = pathinfo(parse_url($row['image'], PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
                        $fileName = 'products/' . $row['sku'] . '.' . $extension;
                        Storage::disk(config('filesystems.public'))->put($fileName, $response->body());
                        $product->image = $fileName;
                        $product->save();
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to download image for simple product", [
                        'sku' => $row['sku'],
                        'image_url' => $row['image'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Attach tags
            $this->attachTags($product, $row, 'destination_room');
            $this->attachTags($product, $row, 'style');
            $this->attachTags($product, $row, 'material');
            $this->attachTags($product, $row, 'color');
            $this->attachTags($product, $row, 'general_feature');

            // Check if temporary skus is set and if so, get all the custom products with that ids
            if (!empty($row['temporary_skus'])) {
                $customProducts = CustomProduct::whereIn('id', explode(',', $row['temporary_skus']))->get();
                foreach ($customProducts as $customProduct) {
                    $orderRow = $customProduct->orderRow;
                    $orderRow->update([
                        'product_id' => $product->id,
                        'custom_product_id' => null,
                    ]);
                    $customProduct->delete();
                }
            }

            Log::info("Simple product imported successfully", ['sku' => $row['sku']]);

        } catch (\Exception $e) {
            Log::error("Failed to import simple product", [
                'sku' => $this->row['sku'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Attach tags for the given type
     */
    protected function attachTags(Product $product, array $row, string $key): void
    {
        if (!empty($row[$key])) {
            $tags = array_map('trim', explode(',', $row[$key]));
            $product->attachTags($tags, $key);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportSimpleProductJob failed after all retries", [
            'sku' => $this->row['sku'] ?? 'unknown',
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
