<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserTypes;
use App\Models\CustomProduct;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'is_admin',
        'is_employee',
        'first_name',
        'last_name',
        'phone',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_admin' => 'boolean',
            'is_employee' => 'boolean',
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Determine if the user is an administrator.
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->is_admin;
    }

    /**
     * Determine if the user is an administrator.
     *
     * @return bool
     */
    public function isEmployee(): bool
    {
        return $this->is_employee;
    }

    /**
     * Get the user type.
     *
     * @return UserTypes
     */
    public function getTypeAttribute(): UserTypes
    {
        return $this->is_admin ? UserTypes::Admin : UserTypes::Member;
    }

    /**
     * Get all the clients that are assigned this user.
     */
    public function clients(): MorphToMany
    {
        return $this->morphedByMany(Client::class, 'userable');
    }

    /**
     * Get all the partners that are assigned this user.
     */
    public function partners(): MorphToMany
    {
        return $this->morphedByMany(Partner::class, 'userable');
    }

    /**
     * Get the collaborator associated with the user.
     */
    public function collaborator(): HasOne
    {
        return $this->hasOne(Collaborator::class);
    }

    /**
     * Get the custom products for the user.
     */
    public function customProducts(): HasMany
    {
        return $this->hasMany(CustomProduct::class);
    }
}
