<?php

namespace App\Models;

use App\Models\Order\Order;
use Illuminate\Database\Eloquent\Model;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Contact extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'departments',
        'position',
        'email',
        'phone',
        'country',
        'languages',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'country' => CountryAlpha3::class,
        'departments' => 'json',
        'languages' => 'json',
    ];

    /**
     * Get all the clients that the contact is related to.
     */
    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class);
    }

    /**
     * Get all the partners that the contact is related to.
     */
    public function partners(): BelongsToMany
    {
        return $this->belongsToMany(Partner::class);
    }

    /**
     * Get all the suppliers that the contact is related to.
     */
    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(Supplier::class);
    }

    /**
     * Get all the brands that the contact is related to.
     */
    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class);
    }

    /**
     * Get the collaborator associated with the contact.
     */
    public function collaborator(): HasOne
    {
        return $this->hasOne(Collaborator::class);
    }

    /**
     * Get all orders where this contact is the internal referent.
     */
    public function ordersAsInternalReferent(): HasMany
    {
        return $this->hasMany(Order::class, 'internal_referent_id');
    }

    /**
     * Get all orders where this contact is a client referent.
     */
    public function ordersAsClientReferent(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'contact_order');
    }

    /**
     * Get all projects where this contact is the internal referent.
     */
    public function projectsAsInternalReferent(): HasMany
    {
        return $this->hasMany(Order::class, 'internal_referent_id');
    }

    /**
     * Get all projects where this contact is a client referent.
     */
    public function projectsAsClientReferent(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'contact_order');
    }

    /**
     * Get the country label name from alpha3 code.
     */
    public function getCountryLabelAttribute($countryAlpha3): string
    {
        if ($countryAlpha3 === null) {
            return '-';
        }
        else {
            return CountryAlpha3::from($countryAlpha3)->getNameInLanguage(LanguageAlpha2::English);
        }
    }
}
