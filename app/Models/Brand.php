<?php

namespace App\Models;

use Spa<PERSON>\Tags\HasTags;
use App\Enums\BrandRating;
use App\Enums\DeliveryTerms;
use <PERSON><PERSON>\Scout\Searchable;
use App\Enums\BrandPriceRange;
use App\Enums\BrandPartnershipLevel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Brand extends Model
{
    use HasFactory, HasTags, Searchable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'prefix',
        'name',
        'price_range',
        'rating',
        'partnership_level',
        'lead_time',
        'purchase_price_list',
        'purchase_conditions',
        'minimum_orderable',
        'extra_costs',
        'delivery_terms',
        'yearly_bonus_info',
        'catalogs',
        'pricelist',
        'valid_from',
        'expected_pricelist_update',
        'social_link',
        'supplier_media_link',
        'supplier_media_link_user',
        'supplier_media_link_password',
        'image',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'price_range' => BrandPriceRange::class,
        'rating' => BrandRating::class,
        'partnership_level' => BrandPartnershipLevel::class,
        'delivery_terms' => DeliveryTerms::class,
        'minimum_orderable' => 'integer',
        'valid_from' => 'date',
        'expected_pricelist_update' => 'date',
    ];

    /**
     * Get the minimum orderable amount.
     *
     * @return float
     */
    public function getMinimumOrderableAttribute(): ?float
    {
        return $this->attributes['minimum_orderable'] ? $this->attributes['minimum_orderable'] / 100 : NULL;
    }

    /**
     * Set the minimum orderable amount.
     *
     * @param float $value
     * @return void
     */
    public function setMinimumOrderableAttribute(?float $value): void
    {
        $this->attributes['minimum_orderable'] = $value === null ? null : (int) ($value * 100);
    }

    /**
     * Get all the suppliers that belong to the brand.
     */
    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(Supplier::class);
    }

    /**
     * Get all the price groups that belong to the brand.
     */
    public function discountGroups(): HasMany
    {
        return $this->hasMany(DiscountGroup::class);
    }

    /**
     * Get all clients that belong to the brand.
     */
    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class);
    }

    /**
     * Get all the partners that belong to the brand.
     */
    public function partners(): BelongsToMany
    {
        return $this->belongsToMany(Partner::class);
    }

    /**
     * Get all the contacts that belong to the brand.
     */
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class);
    }

    /**
     * Get all the products for the brand.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }
}
