<?php

namespace App\Models;

use App\Enums\ClientTypes;
use App\Models\Order\Order;
use App\Enums\DeliveryTerms;
use App\Models\Project\Project;
use App\Enums\CommercialCategories;
use App\Enums\ClientPartnerPriorities;
use Illuminate\Database\Eloquent\Model;
use PrinsFrank\Standards\Country\CountryAlpha3;
use PrinsFrank\Standards\Language\LanguageAlpha2;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Client extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type',

        'partner_id',
        'internal_referent_id',
        'area_manager_id',

        'company',
        'email',
        'country',
        'payment_term_id',
        'commercial_category',
        'priority',
        'minimum_orderable',
        'handling_and_packing',
        'delivery_terms',
        'countries_of_expertise',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'type' => ClientTypes::class,
            'country' => CountryAlpha3::class,
            'commercial_category' => CommercialCategories::class,
            'delivery_terms' => DeliveryTerms::class,
            'priority' => ClientPartnerPriorities::class,
            'handling_and_packing' => 'integer',
            'minimum_orderable' => 'integer',
            'countries_of_expertise' => 'json',
        ];
    }

    /**
     * Get the minimum orderable amount.
     *
     * @return float
     */
    public function getMinimumOrderableAttribute(): ?float
    {
        return $this->attributes['minimum_orderable'] ? $this->attributes['minimum_orderable'] / 100 : NULL;
    }

    /**
     * Set the minimum orderable amount.
     *
     * @param float $value
     * @return void
     */
    public function setMinimumOrderableAttribute(?float $value): void
    {
        $this->attributes['minimum_orderable'] = $value === null ? null : (int) ($value * 100);
    }

    /**
     * Get the internal referent for the client.
     */
    public function internalReferent(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the area manager for the client.
     */
    public function areaManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'area_manager_id');
    }

    /**
     * Get all the users for the client.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable');
    }

    /**
     * Get all the contacts for the client.
     */
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class);
    }

    /**
     * Get all the brands that belong to the client.
     */
    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class);
    }

    /**
     * Get all the addresses for the client.
     */
    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

    /**
     * Get the partner for the client.
     */
    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }

    /**
     * Get all the discount groups for the client.
     */
    public function discountGroups(): BelongsToMany
    {
        return $this->belongsToMany(DiscountGroup::class)
            ->withPivot('discount');
    }

    /**
     * Get the payment term for the client.
     */
    public function paymentTerm(): BelongsTo
    {
        return $this->belongsTo(PaymentTerm::class);
    }

    /**
     * Get the orders for the client.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the projects for the client.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the country label name from alpha3 code.
     */
    public function getCountryLabelAttribute($countryAlpha3): string
    {
        if ($countryAlpha3 === null) {
            return '-';
        }
        else {
            return CountryAlpha3::from($countryAlpha3)->getNameInLanguage(LanguageAlpha2::English);
        }
    }
}
