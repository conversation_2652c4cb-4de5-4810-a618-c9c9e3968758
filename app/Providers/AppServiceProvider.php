<?php

namespace App\Providers;

use App\Models\User;
use App\Models\Order\Order;
use App\Models\Project\Project;
use App\Observers\OrderObserver;
use App\Observers\ProjectObserver;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        /**
         * Check if the user is an admin, and if so, allow them to perform any action.
         */
        Gate::before(function (User $user) {
            if ($user->isAdmin()) {
                return true;
            }
        });

        /**
         * Define the observer for the models.
         */
        Order::observe(OrderObserver::class);
        Project::observe(ProjectObserver::class);
    }
}
