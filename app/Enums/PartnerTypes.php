<?php

namespace App\Enums;

enum PartnerTypes: string
{
    case Agent = 'agent';
    case Company = 'company';
    case PrivateClient = 'private_client';
    case Architect = 'architect';

    public function label()
    {
        return match ($this) {
            static::Agent => 'Agent',
            static::Company => 'Company',
            static::PrivateClient => 'Private Client',
            static::Architect => 'Architect',
        };
    }

    public function icon()
    {
        return match ($this) {
            static::Agent => 'megaphone',
            static::Company => 'building-office',
            static::PrivateClient => 'user',
            static::Architect => 'paint-brush',
        };
    }

    public function color()
    {
        return match ($this) {
            static::Agent => 'cyan',
            static::Company => 'amber',
            static::PrivateClient => 'emerald',
            static::Architect => 'indigo',
        };
    }
}