<?php

namespace App\Enums;

enum ImportTypes: string
{
    case Brands = 'brands';
    case BrandDiscountGroups = 'brand_discount_groups';
    case BrandContacts = 'brand_contacts';

    case Clients = 'clients';
    case ClientAddresses = 'client_addresses';
    case ClientContacts = 'client_contacts';
    case ClientBrands = 'client_brands';
    case ClientDiscountGroups = 'client_discount_groups';

    case Partners = 'partners';
    case PartnerAddresses = 'partner_addresses';
    case PartnerContacts = 'partner_contacts';
    case PartnerBrands = 'partner_brands';
    case PartnerDiscountGroups = 'partner_discount_groups';

    case Suppliers = 'suppliers';
    case SupplierAddresses = 'supplier_addresses';


    public function label()
    {
        return match ($this) {
            // static::Agent => 'Agent',
            static::Brands => 'Brands',
            static::BrandDiscountGroups => 'Brand Discount Groups',
            static::BrandContacts => 'Brand Contacts',

            static::Clients => 'Clients',
            static::ClientAddresses => 'Client Addresses',
            static::ClientContacts => 'Client Contacts',
            static::ClientBrands => 'Client Brands',
            static::ClientDiscountGroups => 'Client Discount Groups',

            static::Partners => 'Partners',
            static::PartnerAddresses => 'Partner Addresses',
            static::PartnerContacts => 'Partner Contacts',
            static::PartnerBrands => 'Partner Brands',
            static::PartnerDiscountGroups => 'Partner Discount Groups',

            static::Suppliers => 'Suppliers',
            static::SupplierAddresses => 'Supplier Addresses',
        };
    }
}
