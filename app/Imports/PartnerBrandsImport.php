<?php

namespace App\Imports;

use App\Models\Brand;
use App\Models\Client;
use App\Models\Partner;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class PartnerBrandsImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        $partner = Partner::findOrFail($row['partner_id'])->first();
        $brand = Brand::where('prefix', $row['brand_prefix'])->first();

        if ($partner && $brand) {
            $existingBrand = $partner->brands()->where('brand_id', $brand->id)->first();
            if (!$existingBrand) {
                $partner->brands()->attach($brand);
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
