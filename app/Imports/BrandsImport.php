<?php

namespace App\Imports;

use Carbon\Carbon;
use App\Models\Brand;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class BrandsImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        Brand::upsert([
            'prefix' => $row['prefix'],
            'name' => $row['name'],
            'price_range' => $row['price_range'],
            'rating' => $row['rating'],
            'partnership_level' => $row['parternship_level'],
            'lead_time' => $row['lead_time'],
            'purchase_price_list' => $row['purchase_price_list'],
            'purchase_conditions' => $row['purchase_conditions'],
            'minimum_orderable' => $row['minimum_orderable'] ? ($row['minimum_orderable'] * 100) : 0, // Convert to minor units.
            'extra_costs' => $row['extra_costs'],
            'delivery_terms' => in_array(strtolower($row['delivery_terms']), array_map(fn($term) => $term->value, \App\Enums\DeliveryTerms::cases())) ? strtolower($row['delivery_terms']) : null,
            'notes' => $row['notes'],
            'yearly_bonus_info' => $row['yearly_bonus_info'],
            'catalogs' => $row['catalogs'],
            'pricelist' => $row['pricelist'],
            'valid_from' => $row['valid_from'] ? Carbon::createFromFormat('d/m/Y', $row['valid_from']) : null,
            'expected_pricelist_update' => $row['expected_pricelist_update'] ? Carbon::createFromFormat('d/m/Y', $row['expected_pricelist_update']) : null,
            'social_link' => $row['social_link'],
            'supplier_media_link' => $row['supplier_media_link'],
            'supplier_media_link_user' => $row['supplier_media_link_user'],
            'supplier_media_link_password' => $row['supplier_media_link_password'],
            'image' => $row['image'] ? 'brands/' . $row['image'] : NULL,
        ], ['prefix']);
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
