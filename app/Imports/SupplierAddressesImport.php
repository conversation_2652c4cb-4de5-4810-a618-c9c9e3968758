<?php

namespace App\Imports;

use App\Models\Address;
use App\Models\Supplier;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class SupplierAddressesImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        $supplier = Supplier::where('code', $row['supplier_code'])->first();

        if ($supplier) {
            Address::create([
                'addressable_id' => $supplier->id,
                'addressable_type' => Supplier::class,
                'name' => $row['name'],
                'type' => in_array(strtolower($row['type']), array_map(fn($type) => $type->value, \App\Enums\AddressTypes::cases())) ? strtolower($row['type']) : \App\Enums\AddressTypes::Invoicing->value,
                'parent_id' => $row['parent_id'],
                'code_invoicing' => $row['code_invoicing'],
                'code_shipping' => $row['code_shipping'],
                'company' => $row['company'],
                'vat_number' => $row['vat_number'],
                'fiscal_code' => $row['fiscal_code'],
                'sdi_code' => $row['sdi_code'],
                'street' => $row['street'],
                'city' => $row['city'], 
                'zip' => $row['zip'],
                'state' => $row['state'],
                'country' => in_array(strtoupper($row['country']), array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases())) ? strtoupper($row['country']) : null,
            ]);
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }
}

