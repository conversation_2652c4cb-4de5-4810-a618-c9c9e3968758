<?php

namespace App\Imports;

use App\Models\Client;
use App\Models\Contact;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class ClientContactsImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        Contact::upsert([
            'name' => $row['name'] ?? '',
            'departments' => empty($row['department']) ? null : json_encode(array_filter(array_map('trim', explode(',', strtolower($row['department']))), function($department) {
                return in_array($department, \App\Enums\ContactPositions::cases());
            })),
            'position' => $row['position'],
            'email' => $row['email'],
            'phone' => $row['phone'],
            'country' => in_array(strtoupper($row['country']), array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases())) ? strtoupper($row['country']) : null,
            'languages' => empty($row['languages']) ? null : json_encode(array_filter(array_map('trim', explode(',', strtolower($row['languages']))), function($language) {
                return in_array($language, array_map(fn($lang) => $lang->value, \PrinsFrank\Standards\Language\LanguageAlpha2::cases()));
            })),
            'notes' => $row['notes'],
        ], ['email']);

        $contact = Contact::where('email', $row['email'])->first();
        $client = Client::findOrFail($row['client_id'])->first();

        if ($client && $contact) {
            if (!$contact->clients()->where('client_id', $client->id)->exists()) {
                $contact->clients()->attach($client);
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
