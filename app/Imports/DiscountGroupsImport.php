<?php

namespace App\Imports;

use App\Models\Brand;
use App\Models\DiscountGroup;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class DiscountGroupsImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        if (DiscountGroup::where('code', $row['code'])->exists()) {
            return;
        }
        
        $discountGroup = DiscountGroup::create([
            'code' => $row['code'],
            'discount' => $row['discount'] ?? 0,
            'description' => $row['description'] ?? NULL,
        ]);

        $brand = Brand::where('prefix', $row['brand_prefix'])->first();

        if ($brand) {
            $discountGroup->brand()->associate($brand);
            $discountGroup->save();
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
