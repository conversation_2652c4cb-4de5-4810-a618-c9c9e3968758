<?php

namespace App\Imports;

use App\Models\Client;
use Maat<PERSON><PERSON>ite\Excel\Row;
use App\Models\DiscountGroup;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class ClientDiscountGroupsImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        $client = Client::findOrFail($row['client_id'])->first();
        $discountGroup = DiscountGroup::where('code', $row['discount_group_code'])->first();

        if ($client && $discountGroup) {
            $existingDiscountGroup = $client->discountGroups()->where('code', $row['discount_group_code'])->first();
            if ($existingDiscountGroup) {
                $client->discountGroups()->updateExistingPivot($discountGroup->id, [
                    'discount' => $row['discount'] ?? 0,
                    'extra_discount_threshold' => $row['extra_discount_threshold'],
                    'extra_discount' => $row['extra_discount'],
                ]);
            } 
            else {
                $client->discountGroups()->attach($discountGroup, [
                    'discount' => $row['discount'] ?? 0,
                    'extra_discount_threshold' => $row['extra_discount_threshold'],
                    'extra_discount' => $row['extra_discount'],
                ]);
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
