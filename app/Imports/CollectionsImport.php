<?php

namespace App\Imports;

use App\Models\Collection;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class CollectionsImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        Collection::upsert([
            'code' => $row['code'],
            'name' => $row['name'],
            'image' => $row['image'] ? 'collections/' . $row['image'] : NULL,
        ], ['code']);
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
