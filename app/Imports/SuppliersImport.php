<?php

namespace App\Imports;

use App\Models\Brand;
use App\Models\Supplier;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;

class SuppliersImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;
    
    public function onRow(Row $row): void
    {
        Supplier::upsert([
            'code' => $row['code'],
            'name' => $row['name'],
            'payment_conditions' => $row['payment_conditions'],
        ], ['code']);

        $supplier = Supplier::where('code', $row['code'])->first();

        $brand = Brand::where('prefix', $row['brand_prefix'])->first();

        if ($brand) {
            if (!$supplier->brands()->where('brand_id', $brand->id)->exists()) {
                $supplier->brands()->attach($brand);
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
