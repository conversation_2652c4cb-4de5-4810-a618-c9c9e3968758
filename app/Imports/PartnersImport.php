<?php

namespace App\Imports;

use App\Models\Partner;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class PartnersImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        Partner::create([
            'type' => in_array(strtolower($row['type']), array_map(fn($type) => $type->value, \App\Enums\PartnerTypes::cases())) ? strtolower($row['type']) : \App\Enums\PartnerTypes::Agent->value,
            'internal_referent_id' => $row['internal_referent_email'] ? \App\Models\User::where('email', $row['internal_referent_email'])->value('id') : null,
            'company' => $row['company'],
            'email' => $row['email'] ?? '-',
            'country' => in_array(strtoupper($row['country']), array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases())) ? strtoupper($row['country']) : null,
            'payment_term_id' => $row['payment_term_code'] ? \App\Models\PaymentTerm::where('code', $row['payment_term_code'])->value('id') : null,
            'commercial_category' => in_array(strtolower($row['commercial_category']), array_map(fn($category) => $category->value, \App\Enums\CommercialCategories::cases())) ? strtolower($row['commercial_category']) : null,
            'priority' => in_array(strtolower($row['priority']), array_map(fn($priority) => $priority->value, \App\Enums\ClientPartnerPriorities::cases())) ? strtolower($row['priority']) : null,
            'minimum_orderable' => $row['minimum_orderable'] ? ($row['minimum_orderable'] * 100) : 0, // Convert to minor units.
            'handling_and_packing' => $row['handling_and_packing'],
            'delivery_terms' => in_array(strtolower($row['delivery_terms']), array_map(fn($term) => $term->value, \App\Enums\DeliveryTerms::cases())) ? strtolower($row['delivery_terms']) : null,
            'countries_of_expertise' => empty($row['countries_of_expertise']) ? null : json_encode(array_filter(array_map('trim', explode(',', strtoupper($row['countries_of_expertise']))), function($country) {
                return in_array($country, array_map(fn($country) => $country->value, \PrinsFrank\Standards\Country\CountryAlpha3::cases()));
            })),
            'notes' => $row['notes'],
        ]);
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
