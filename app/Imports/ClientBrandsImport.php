<?php

namespace App\Imports;

use App\Models\Brand;
use App\Models\Client;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class ClientBrandsImport implements OnEachRow, WithHeadingRow, WithChunkReading, WithProgressBar
{
    use Importable;

    public function onRow(Row $row): void
    {
        $client = Client::findOrFail($row['client_id'])->first();
        $brand = Brand::where('prefix', $row['brand_prefix'])->first();

        if ($client && $brand) {
            $existingBrand = $client->brands()->where('brand_id', $brand->id)->first();
            if (!$existingBrand) {
                $client->brands()->attach($brand);
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }
}
