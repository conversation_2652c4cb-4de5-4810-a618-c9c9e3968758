<?php

namespace App\Console\Commands;

use App\Models\Brand;
use Illuminate\Console\Command;
use App\Jobs\SyncBrandTagsFromProductsJob;

class SyncBrandTagsFromProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'brands:sync-tags-from-products
                            {--brand= : Sync tags for a specific brand ID}
                            {--force : Force to clear existing tags before syncing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Queue brand tags synchronization jobs (type, material, destination_room, style) from their products';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting brand tags synchronization from products...');

        $brandId = $this->option('brand');
        $force = $this->option('force');

        if ($brandId) {
            $brand = Brand::find($brandId);
            if (!$brand) {
                $this->error("Brand with ID {$brandId} not found.");
                return 1;
            }
            $brands = collect([$brand]);
            $this->info("Queuing job for single brand: {$brand->name}");
        } else {
            $brands = Brand::all();
            $this->info("Queuing jobs for all brands ({$brands->count()} total)");
        }

        $jobsQueued = 0;

        foreach ($brands as $brand) {
            SyncBrandTagsFromProductsJob::dispatch($brand, $force);
            $jobsQueued++;
        }

        $this->info("Successfully queued {$jobsQueued} jobs for brand tag synchronization!");
        $this->info("Jobs will be processed by queue workers.");
    }
}
