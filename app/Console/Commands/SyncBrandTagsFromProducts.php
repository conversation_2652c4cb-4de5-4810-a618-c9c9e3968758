<?php

namespace App\Console\Commands;

use App\Models\Brand;
use Illuminate\Console\Command;

class SyncBrandTagsFromProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'brands:sync-tags-from-products
                            {--brand= : Sync tags for a specific brand ID}
                            {--force : Force sync even if brand already has tags}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync brand tags (type, material, destination_room, style) from their products';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting brand tags synchronization from products...');

        $brandId = $this->option('brand');
        $force = $this->option('force');

        if ($brandId) {
            $brand = Brand::find($brandId);
            if (!$brand) {
                $this->error("Brand with ID {$brandId} not found.");
                return 1;
            }
            $brands = collect([$brand]);
            $this->info("Processing single brand: {$brand->name}");
        } else {
            $brands = Brand::with(['products.tags'])->get();
            $this->info("Processing all brands ({$brands->count()} total)");
        }

        $progressBar = $this->output->createProgressBar($brands->count());
        $progressBar->start();

        $syncedCount = 0;
        $skippedCount = 0;

        foreach ($brands as $brand) {
            $result = $this->syncBrandTags($brand, $force);

            if ($result) {
                $syncedCount++;
            } else {
                $skippedCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info("Synchronization completed!");
        $this->info("Brands synced: {$syncedCount}");
        $this->info("Brands skipped: {$skippedCount}");

        return 0;
    }

    /**
     * Sync tags for a specific brand from its products
     */
    private function syncBrandTags(Brand $brand, bool $force = false): bool
    {
        // Load products with tags if not already loaded
        if (!$brand->relationLoaded('products')) {
            $brand->load(['products.tags']);
        }

        // Check if brand already has tags and force is not enabled
        if (!$force && $brand->tags()->whereIn('type', ['type', 'material', 'destination_room', 'style'])->exists()) {
            return false; // Skip if brand already has tags
        }

        // Get all unique tags from products for the 4 specific types
        $tagTypes = ['type', 'material', 'destination_room', 'style'];
        $allProductTags = collect();

        foreach ($brand->products as $product) {
            foreach ($tagTypes as $tagType) {
                $productTags = $product->tags->where('type', $tagType);
                $allProductTags = $allProductTags->merge($productTags);
            }
        }

        // Remove duplicates based on name and type
        $uniqueTags = $allProductTags->unique(function ($tag) {
            return $tag->type . '|' . $tag->name;
        });

        if ($uniqueTags->isEmpty()) {
            return false; // No tags to sync
        }

        // Remove existing tags of these types if force is enabled
        if ($force) {
            $brand->tags()->whereIn('type', $tagTypes)->detach();
        }

        // Attach the unique tags to the brand
        foreach ($uniqueTags as $tag) {
            // Check if brand already has this specific tag
            if (!$brand->tags()->where('tags.id', $tag->id)->exists()) {
                $brand->attachTag($tag);
            }
        }

        return true;
    }
}
