<?php

namespace App\Console\Commands\Data\Relations;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\ImportSupplierAddressJob;

class ImportSupplierAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'supplier-addresses:import
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import supplier addresses from Excel file using queue jobs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting supplier addresses import from Excel file: {$filePath}");

        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            $fileContent = Storage::disk($disk)->get($filePath);
            $tempPath = tempnam(sys_get_temp_dir(), 'supplier_addresses_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            $rows = SimpleExcelReader::create($tempPath)->getRows()->toArray();
            unlink($tempPath);

            if (empty($rows)) {
                $this->error("No data found in Excel file.");
                return 1;
            }

            $this->info("Found " . count($rows) . " rows to process");
            $this->info("Queuing jobs for supplier addresses import...");

            $jobsQueued = 0;
            foreach ($rows as $row) {
                $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();
                if (empty(array_filter($cleanRow))) continue;

                ImportSupplierAddressJob::dispatch($cleanRow);
                $jobsQueued++;
            }

            $this->info("Successfully queued {$jobsQueued} jobs for supplier addresses import!");
            $this->info("Jobs will be processed by queue workers.");
            $this->info("Monitor progress with: php artisan queue:work");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to process Excel file: " . $e->getMessage());
            return 1;
        }
    }
}
