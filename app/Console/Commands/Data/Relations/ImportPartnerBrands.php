<?php

namespace App\Console\Commands\Data\Relations;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\ImportPartnerBrandJob;

class ImportPartnerBrands extends Command
{
    protected $signature = 'partner-brands:import {file} {--disk=private}';
    protected $description = 'Import partner brands from Excel file using queue jobs';

    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            $fileContent = Storage::disk($disk)->get($filePath);
            $tempPath = tempnam(sys_get_temp_dir(), 'partner_brands_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            $rows = SimpleExcelReader::create($tempPath)->getRows()->toArray();
            unlink($tempPath);

            $jobsQueued = 0;
            foreach ($rows as $row) {
                $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();
                if (empty(array_filter($cleanRow))) continue;

                ImportPartnerBrandJob::dispatch($cleanRow);
                $jobsQueued++;
            }

            $this->info("Successfully queued {$jobsQueued} jobs for partner brands import!");
            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to process Excel file: " . $e->getMessage());
            return 1;
        }
    }
}
