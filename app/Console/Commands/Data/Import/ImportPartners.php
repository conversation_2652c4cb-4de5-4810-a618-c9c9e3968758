<?php

namespace App\Console\Commands\Data\Import;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use <PERSON>tie\SimpleExcel\SimpleExcelReader;
use App\Jobs\ImportPartnerJob;

class ImportPartners extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'partners:import
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import partners from Excel file using queue jobs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting partners import from Excel file: {$filePath}");

        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            $fileContent = Storage::disk($disk)->get($filePath);
            $tempPath = tempnam(sys_get_temp_dir(), 'partners_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            $rows = SimpleExcelReader::create($tempPath)->getRows()->toArray();
            unlink($tempPath);

            if (empty($rows)) {
                $this->error("No data found in Excel file.");
                return 1;
            }

            $this->info("Found " . count($rows) . " rows to process");
            $this->info("Queuing jobs for partners import...");

            $jobsQueued = 0;
            foreach ($rows as $row) {
                $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();
                if (empty(array_filter($cleanRow))) continue;

                ImportPartnerJob::dispatch($cleanRow);
                $jobsQueued++;
            }

            $this->info("Successfully queued {$jobsQueued} jobs for partners import!");
            $this->info("Jobs will be processed by queue workers.");
            $this->info("Monitor progress with: php artisan queue:work");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to process Excel file: " . $e->getMessage());
            return 1;
        }
    }
}
