<?php

namespace App\Console\Commands\Data\Import;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Jobs\ImportBrandTagsJob;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ImportBrandTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'brands:import-tags
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import brand tags from Excel file with columns: BRAND_PREFIX, TYPE, MATERIAL, DESTINATION_ROOM, STYLE';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting brand tags import from Excel file: {$filePath}");

        // Check if file exists
        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            // Get file content and create temporary file (works with R2 and other remote storage)
            $fileContent = Storage::disk($disk)->get($filePath);
            $tempPath = tempnam(sys_get_temp_dir(), 'brand_tags_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            // Load the spreadsheet from temporary file
            $spreadsheet = IOFactory::load($tempPath);
            $worksheet = $spreadsheet->getActiveSheet();

            // Get all data as array
            $data = $worksheet->toArray();

            // Clean up temporary file
            unlink($tempPath);

            if (empty($data)) {
                $this->error("Excel file is empty or could not be read.");
                return 1;
            }

            // Get headers from first row
            $headers = array_map('trim', $data[0]);

            // Validate required columns
            $requiredColumns = ['BRAND_PREFIX', 'TYPE', 'MATERIAL', 'DESTINATION_ROOM', 'STYLE'];
            $missingColumns = array_diff($requiredColumns, $headers);

            if (!empty($missingColumns)) {
                $this->error("Missing required columns: " . implode(', ', $missingColumns));
                $this->info("Found columns: " . implode(', ', $headers));
                return 1;
            }

            // Remove header row
            array_shift($data);

            if (empty($data)) {
                $this->error("No data rows found in Excel file.");
                return 1;
            }

            $this->info("Found " . count($data) . " data rows to process");
            $this->info("Queuing jobs for brand tags import...");

            $jobsQueued = 0;

            // Process each row
            foreach ($data as $rowData) {
                // Skip empty rows
                if (empty(array_filter($rowData))) {
                    continue;
                }

                // Create associative array with headers
                $row = array_combine($headers, $rowData);

                // Queue job for this row
                ImportBrandTagsJob::dispatch($row);
                $jobsQueued++;
            }

            $this->info("Successfully queued {$jobsQueued} jobs for brand tags import!");
            $this->info("Jobs will be processed by queue workers.");
            $this->info("Monitor progress with: php artisan queue:work");

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to process Excel file: " . $e->getMessage());
            return 1;
        }
    }
}
