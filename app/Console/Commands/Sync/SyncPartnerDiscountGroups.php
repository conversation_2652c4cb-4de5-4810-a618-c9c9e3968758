<?php

namespace App\Console\Commands\Sync;

use App\Imports\PartnerDiscountGroupsImport;
use Illuminate\Console\Command;

class SyncPartnerDiscountGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-partner-discount-groups';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync partner discount groups';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting discount groups sync...');

        (new PartnerDiscountGroupsImport)->withOutput($this->output)->import('imports/partner-discount-groups.xlsx', config('filesystems.private'));

        $this->output->success('Discount groups synced successfully!');
    }
}
