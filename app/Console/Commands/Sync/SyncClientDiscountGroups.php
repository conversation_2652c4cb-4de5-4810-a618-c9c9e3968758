<?php

namespace App\Console\Commands\Sync;

use App\Imports\ClientDiscountGroupsImport;
use Illuminate\Console\Command;

class SyncClientDiscountGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-client-discount-groups';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync client discount groups';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting discount groups sync...');

        (new ClientDiscountGroupsImport)->withOutput($this->output)->import('imports/client-discount-groups.xlsx', config('filesystems.private'));

        $this->output->success('Discount groups synced successfully!');
    }
}
