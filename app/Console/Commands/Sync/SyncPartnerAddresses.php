<?php

namespace App\Console\Commands\Sync;

use App\Imports\PartnerAddressesImport;
use Illuminate\Console\Command;

class SyncPartnerAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-partner-addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync partner addresses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting addresses sync...');

        (new PartnerAddressesImport)->withOutput($this->output)->import('imports/partner-addresses.xlsx', config('filesystems.private'));

        $this->output->success('Addresses synced successfully!');
    }
}
