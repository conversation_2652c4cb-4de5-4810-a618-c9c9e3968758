<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\SuppliersImport;
use Illuminate\Support\Facades\Storage;

class SyncSuppliers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'suppliers:import-from-excel
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import suppliers from Excel file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting suppliers import from Excel file: {$filePath}");

        // Check if file exists
        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            $this->output->title('Starting suppliers import...');

            (new SuppliersImport)->withOutput($this->output)->import($filePath, $disk);

            $this->output->success('Suppliers imported successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to import suppliers: " . $e->getMessage());
            return 1;
        }
    }
}
