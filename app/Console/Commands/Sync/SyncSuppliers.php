<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\SuppliersImport;
use Illuminate\Support\Facades\Storage;

class SyncSuppliers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-suppliers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync suppliers from the source to the destination';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting suppliers sync...');

        (new SuppliersImport)->withOutput($this->output)->import('imports/suppliers.xlsx', config('filesystems.private'));

        $this->output->success('Suppliers synced successfully!');
    }
}
