<?php

namespace App\Console\Commands\Sync;

use App\Imports\ClientsImport;
use Illuminate\Console\Command;

class SyncClients extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-clients';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync clients';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting clients sync...');

        (new ClientsImport)->withOutput($this->output)->import('imports/clients.xlsx', config('filesystems.private'));

        $this->output->success('Clients synced successfully!');
    }
}
