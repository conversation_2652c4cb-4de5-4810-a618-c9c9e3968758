<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\BrandContactsImport;

class SyncBrandContacts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-brand-contacts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync brand contacts from the latest list';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting contacts sync...');

        (new BrandContactsImport)->withOutput($this->output)->import('imports/brand-contacts.xlsx', config('filesystems.private'));

        $this->output->success('Contacts synced successfully!');
    }
}
