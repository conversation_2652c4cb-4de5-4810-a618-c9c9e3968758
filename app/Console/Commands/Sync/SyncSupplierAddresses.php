<?php

namespace App\Console\Commands\Sync;

use App\Imports\SupplierAddressesImport;
use Illuminate\Console\Command;

class SyncSupplierAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-supplier-addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync supplier addresses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting addresses sync...');

        (new SupplierAddressesImport)->withOutput($this->output)->import('imports/supplier-addresses.xlsx', config('filesystems.private'));

        $this->output->success('Addresses synced successfully!');
    }
}
