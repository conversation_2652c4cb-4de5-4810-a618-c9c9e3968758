<?php

namespace App\Console\Commands\Sync;

use App\Imports\PartnersImport;
use Illuminate\Console\Command;

class SyncPartners extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-partners';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync partners';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting partners sync...');

        (new PartnersImport)->withOutput($this->output)->import('imports/partners.xlsx', config('filesystems.private'));

        $this->output->success('Partners synced successfully!');
    }
}
