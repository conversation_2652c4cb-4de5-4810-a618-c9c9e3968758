<?php

namespace App\Console\Commands\Sync;

use App\Models\PaymentTerm;
use App\Models\PaymentTermItem;
use Illuminate\Console\Command;

class ImportPaymentTerms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment-terms:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create all the payment terms in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $paymentTerms = [
            ['code' => 'BA10', 'name' => 'Deposit 10%, Bal.Bef.Shipment'],
            ['code' => 'BA20', 'name' => 'Deposit 20%, Bal.Bef.Shipment'],
            ['code' => 'BA30', 'name' => 'Deposit 30%, Bal.Bef.Shipment'],
            ['code' => 'BA40', 'name' => 'Deposit 40%, Bal.Bef.Shipment'],
            ['code' => 'BA50', 'name' => 'Deposit 50%, Bal.Bef.Shipment'],
            ['code' => 'BA60', 'name' => 'Deposit 60%, Bal.Bef.Shipment'],
            ['code' => 'BA70', 'name' => 'Deposit 70%, Bal.Bef.Shipment'],
            ['code' => 'BA80', 'name' => 'Deposit 80%, Bal.Bef.Shipment'],
            ['code' => 'BA90', 'name' => 'Deposit 90%, Bal.Bef.Shipment'],
            ['code' => 'BA100', 'name' => 'Payment in Deposit 100%'],
            ['code' => 'B00VF', 'name' => 'No Deposit, Bal.Bef.Shipment'],
            ['code' => 'B30DF', 'name' => 'Bank Transfer 30 days DF'],
            ['code' => 'B45DF', 'name' => 'Bank Transfer 45 days DF'],
            ['code' => 'B60DF', 'name' => 'Bank Transfer 60 days DF'],
            ['code' => 'B90DF', 'name' => 'Bank Transfer 90 days DF'],
        ];

        $paymentTermItems = [
            ['payment_term_id' => 21, 'description' => '10% - Deposit', 'percentage' => 10],
            ['payment_term_id' => 21, 'description' => '90% - Balance Before Shipment', 'percentage' => 90],
            ['payment_term_id' => 22, 'description' => '20% - Deposit', 'percentage' => 20],
            ['payment_term_id' => 22, 'description' => '80% - Balance Before Shipment', 'percentage' => 80],
            ['payment_term_id' => 23, 'description' => '30% - Deposit', 'percentage' => 30],
            ['payment_term_id' => 23, 'description' => '70% - Balance Before Shipment', 'percentage' => 70],
            ['payment_term_id' => 24, 'description' => '40% - Deposit', 'percentage' => 40],
            ['payment_term_id' => 24, 'description' => '60% - Balance Before Shipment', 'percentage' => 60],
            ['payment_term_id' => 25, 'description' => '50% - Deposit', 'percentage' => 50],
            ['payment_term_id' => 25, 'description' => '50% - Balance Before Shipment', 'percentage' => 50],
            ['payment_term_id' => 26, 'description' => '60% - Deposit', 'percentage' => 60],
            ['payment_term_id' => 26, 'description' => '40% - Balance Before Shipment', 'percentage' => 40],
            ['payment_term_id' => 27, 'description' => '70% - Deposit', 'percentage' => 70],
            ['payment_term_id' => 27, 'description' => '30% - Balance Before Shipment', 'percentage' => 30],
            ['payment_term_id' => 28, 'description' => '80% - Deposit', 'percentage' => 80],
            ['payment_term_id' => 28, 'description' => '20% - Balance Before Shipment', 'percentage' => 20],
            ['payment_term_id' => 29, 'description' => '90% - Deposit', 'percentage' => 90],
            ['payment_term_id' => 29, 'description' => '10% - Balance Before Shipment', 'percentage' => 10],
            ['payment_term_id' => 30, 'description' => '100% - Payment in Advance', 'percentage' => 100],
            ['payment_term_id' => 31, 'description' => '100% - Balance Before Shipment', 'percentage' => 100],
            ['payment_term_id' => 32, 'description' => '100% - Bank Transfer 30 Days From Invoice', 'percentage' => 100],
            ['payment_term_id' => 33, 'description' => '100% - Bank Transfer 45 Days From Invoice', 'percentage' => 100],
            ['payment_term_id' => 34, 'description' => '100% - Bank Transfer 60 Days From Invoice', 'percentage' => 100],
            ['payment_term_id' => 35, 'description' => '100% - Bank Transfer 90 Days From Invoice', 'percentage' => 100],
        ];

        PaymentTermItem::truncate();
        PaymentTerm::truncate();

        PaymentTerm::insert($paymentTerms);
        PaymentTermItem::insert($paymentTermItems);

        $this->output->success('Payment Terms synced successfully!');
    }
}
