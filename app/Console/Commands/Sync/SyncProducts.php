<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Jobs\ImportSimpleProductJob;
use App\Jobs\ImportVariantProductJob;
use App\Jobs\ImportModularProductJob;
use App\Jobs\ImportModularOptionJob;

class SyncProducts extends Command
{
    protected $signature = 'app:sync-products {type?}';

    protected $description = 'Sync all the products';

    public function handle()
    {
        $type = $this->argument('type');

        if ($type === 'simple' || is_null($type)) {
            $this->output->title('Starting simple products sync...');
            $this->sync('simple_products', ImportSimpleProductJob::class);
        }

        if ($type === 'variant' || is_null($type)) {
            $this->output->title('Starting variant products sync...');
            $this->sync('variant_products', ImportVariantProductJob::class);
        }

        if ($type === 'modular' || is_null($type)) {
            $this->output->title('Starting modular options sync...');
            $this->sync('modular_options', ImportModularOptionJob::class);

            $this->output->title('Starting modular products sync...');
            $this->sync('modular_products', ImportModularProductJob::class);
        }
    }

    protected function sync(string $feedKey, string $jobClass): void
    {
        $url = config("app.feeds.$feedKey");
        $fileName = "imports/products/{$feedKey}.xlsx";

        // Download the Excel file
        $this->output->writeln('Downloading Excel file...');
        $response = Http::get($url);
        if ($response->successful()) {
            Storage::disk(config('filesystems.private'))->put($fileName, $response->body());
        } else {
            $this->output->error('Failed to download Excel file');
            return;
        }

        // Read all rows from Excel
        $this->output->writeln('Reading Excel file...');
        try {
            $fileContent = Storage::disk(config('filesystems.private'))->get($fileName);
            $tempPath = tempnam(sys_get_temp_dir(), 'excel_import_') . '.xlsx';
            file_put_contents($tempPath, $fileContent);

            $rows = SimpleExcelReader::create($tempPath)
                ->getRows()
                ->toArray();

            // Clean up temporary file
            unlink($tempPath);
        } catch (\Exception $e) {
            $this->output->error('Failed to read Excel file: ' . $e->getMessage());
            return;
        }

        $totalRows = count($rows);
        $this->output->writeln("Found {$totalRows} items to import");

        // Dispatch jobs for each row
        $this->output->writeln('Dispatching jobs to queue...');
        $jobsDispatched = 0;

        foreach ($rows as $row) {
            // Clean the row data
            $cleanRow = collect($row)->map(fn($v) => trim((string) $v) === '' ? null : $v)->toArray();

            // Only dispatch job if SKU exists
            if (!empty($cleanRow['sku'])) {
                $jobClass::dispatch($cleanRow);
                $jobsDispatched++;
            }
        }

        $this->newLine();
        $this->output->success("Successfully dispatched {$jobsDispatched} import jobs to the queue!");
        $this->output->writeln("Jobs will be processed by queue workers.");
    }
}
