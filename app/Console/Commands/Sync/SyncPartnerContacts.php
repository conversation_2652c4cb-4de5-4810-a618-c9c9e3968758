<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\PartnerContactsImport;

class SyncPartnerContacts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-partner-contacts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync partner contacts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting contacts sync...');

        (new PartnerContactsImport)->withOutput($this->output)->import('imports/partner-contacts.xlsx', config('filesystems.private'));

        $this->output->success('Contacts synced successfully!');
    }
}
