<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\DiscountGroupsImport;
use Illuminate\Support\Facades\Storage;

class SyncDiscountGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'discount-groups:import-from-excel
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import discount groups from Excel file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting discount groups import from Excel file: {$filePath}");

        // Check if file exists
        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            $this->output->title('Starting discount groups import...');

            (new DiscountGroupsImport)->withOutput($this->output)->import($filePath, $disk);

            $this->output->success('Discount groups imported successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to import discount groups: " . $e->getMessage());
            return 1;
        }
    }
}
