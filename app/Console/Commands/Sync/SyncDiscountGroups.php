<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\DiscountGroupsImport;

class SyncDiscountGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-discount-groups';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync discount groups from the latest pricelist';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting discount groups sync...');

        (new DiscountGroupsImport)->withOutput($this->output)->import('imports/discount-groups.xlsx', config('filesystems.private'));

        $this->output->success('Discount groups synced successfully!');
    }
}
