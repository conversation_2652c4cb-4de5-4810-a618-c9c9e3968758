<?php

namespace App\Console\Commands\Sync;

use App\Imports\ClientBrandsImport;
use Illuminate\Console\Command;

class SyncClientBrands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-client-brands';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync client brands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting brands sync...');

        (new ClientBrandsImport)->withOutput($this->output)->import('imports/client-brands.xlsx', config('filesystems.private'));

        $this->output->success('Brands synced successfully!');
    }
}
