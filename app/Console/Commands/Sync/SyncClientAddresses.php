<?php

namespace App\Console\Commands\Sync;

use App\Imports\ClientAddressesImport;
use Illuminate\Console\Command;

class SyncClientAddresses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-client-addresses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync client addresses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting addresses sync...');

        (new ClientAddressesImport)->withOutput($this->output)->import('imports/client-addresses.xlsx', config('filesystems.private'));

        $this->output->success('Addresses synced successfully!');
    }
}
