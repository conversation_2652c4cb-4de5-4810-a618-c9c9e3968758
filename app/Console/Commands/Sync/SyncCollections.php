<?php

namespace App\Console\Commands\Sync;

use App\Imports\CollectionsImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncCollections extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-collections';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync collections';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting collections sync...');

        (new CollectionsImport)->withOutput($this->output)->import('imports/collections.xlsx', config('filesystems.private'));

        $this->output->success('Collections synced successfully!');
    }
}
