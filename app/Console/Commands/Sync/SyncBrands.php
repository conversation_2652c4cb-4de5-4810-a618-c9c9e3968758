<?php

namespace App\Console\Commands\Sync;

use App\Imports\BrandsImport;
use Illuminate\Console\Command;

class SyncBrands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-brands';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync brands from the source to the destination';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting brands sync...');

        (new BrandsImport)->withOutput($this->output)->import('imports/brands.xlsx', config('filesystems.private'));

        $this->output->success('Brands synced successfully!');
    }
}
