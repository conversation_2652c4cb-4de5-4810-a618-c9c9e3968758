<?php

namespace App\Console\Commands\Sync;

use App\Imports\BrandsImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncBrands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'brands:import-from-excel
                            {file : Path to the Excel file}
                            {--disk=private : Storage disk to use (local, public, private)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import brands from Excel file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $disk = $this->option('disk');

        $this->info("Starting brands import from Excel file: {$filePath}");

        // Check if file exists
        if (!Storage::disk($disk)->exists($filePath)) {
            $this->error("File not found: {$filePath} on disk: {$disk}");
            return 1;
        }

        try {
            $this->output->title('Starting brands import...');

            (new BrandsImport)->withOutput($this->output)->import($filePath, $disk);

            $this->output->success('Brands imported successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to import brands: " . $e->getMessage());
            return 1;
        }
    }
}
