<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\PartnerBrandsImport;

class SyncPartnerBrands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-partner-brands';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync partner brands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting brands sync...');

        (new PartnerBrandsImport)->withOutput($this->output)->import('imports/partner-brands.xlsx', config('filesystems.private'));

        $this->output->success('Brands synced successfully!');
    }
}
