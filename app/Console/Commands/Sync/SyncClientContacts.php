<?php

namespace App\Console\Commands\Sync;

use Illuminate\Console\Command;
use App\Imports\ClientContactsImport;

class SyncClientContacts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-client-contacts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync client contacts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting contacts sync...');

        (new ClientContactsImport)->withOutput($this->output)->import('imports/client-contacts.xlsx', config('filesystems.private'));

        $this->output->success('Contacts synced successfully!');
    }
}
