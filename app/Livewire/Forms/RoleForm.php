<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleForm extends Form
{
    public $isEditMode = false;

    public ?Role $role;
    public $name = '';
    public $permissions = [];

    public function rules()
    {
        return [
            'name' => 'required|string|min:3|lowercase|unique:roles,name,' . ($this->role->id ?? 'NULL') . '|regex:/^[a-z0-9-_]+$/',
        ];
    }

    public function store()
    {
        $this->validate();

        //Creo il ruolo
        $role = Role::create(['name' => $this->name]);

        //Assegno i permessi selezionati
        $this->assignPermissions($role);

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->role->name = $this->name;
        $this->role->save();

        // Rimuovo tutti i permessi e assegno quelli selezionati
        $this->role->permissions()->detach();
        $this->assignPermissions($this->role);

        $this->reset();
    }

    private function assignPermissions(Role $role)
    {
        foreach ($this->permissions as $resource => $actions) {
            foreach ($actions as $action => $isEnabled) {
                if ($isEnabled) {
                    // Creo il permesso se non esiste
                    $permissionName = "{$action}_{$resource}";
                    $permission = Permission::firstOrCreate(['name' => $permissionName]);

                    // Assegno il permesso al ruolo
                    $role->givePermissionTo($permission);
                }
            }
        }
    }

    public function setRole(Role $role)
    {
        $this->isEditMode = true;
        $this->role = $role;
        $this->name = $role->name;

        // recupero i permessi assegnati al ruolo
        $permissions = $role->permissions->pluck('name')->toArray();

        collect($this->permissions)->keys()->each(function ($resource) use ($permissions) {
            $this->permissions[$resource]['read'] = in_array("read_{$resource}", $permissions);
            $this->permissions[$resource]['write'] = in_array("write_{$resource}", $permissions);
        });

    }
}
