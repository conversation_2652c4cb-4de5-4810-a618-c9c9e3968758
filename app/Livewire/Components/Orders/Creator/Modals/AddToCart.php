<?php

namespace App\Livewire\Components\Orders\Creator\Modals;

use App\Models\Module;
use Flux\Flux;
use App\Models\Option;
use App\Models\Product;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Computed;

class AddToCart extends Component
{
    public Product $product;

    public $selectedCategories = [];
    public $selectedOptions = [];

    public ?int $selectedQuantity;
    
    public function render()
    {
        return view('livewire.components.orders.creator.modals.add-to-cart');
    }

    #[On('add-to-cart-show')]
    public function show(Product $product)
    {   
        // Close all modals
        Flux::modals()->close();

        // Reset the modal
        $this->resetAddToCart();

        // Load the product
        $this->product = $product;

        if ($this->product->hasModules()) {
            // Load the modules
            $this->product->load('modules');
            
            // Initialize selected categories and options arrays with proper structure
            $moduleCount = $this->product->modules->count();
            $this->selectedCategories = array_fill(0, $moduleCount, '');
            $this->selectedOptions = array_fill(0, $moduleCount, '');
        }

        Flux::modal('add-to-cart')->show();
    }

    public function resetAddToCart(): void
    {
        $this->resetValidation();

        $this->product = new Product();

        $this->selectedQuantity = null;

        $this->selectedCategories = [];
        $this->selectedOptions = [];
    }

    #[Computed]
    public function fetchCategories(Module $module)
    {
        $categories = $module->options->pluck('category')->unique();

        return $categories;
    }

    #[Computed]
    public function fetchOptions($value)
    {
        $options = Option::query()
            ->where('category', $value)
            ->select('id', 'sku', 'code', 'description', 'category', 'image')
            ->get();

        return $options;
    }

    public function updatedSelectedCategories($value, $key)
    {
        // Reset the selected option for this module
        $this->selectedOptions[$key] = '';
    }

    public function getModularProductSku(): string
    {
        // Concat the selected options SKUs to create the modular product SKU
        return implode(collect($this->selectedOptions)->pluck('sku')->toArray());
    }

    public function addToCart()
    {
        $rules = [
            'selectedQuantity' => [
                'required', 'numeric', 'min:1',
                function ($attribute, $value, $fail) {
                    $multiple = $this->product->purchase_units ?? 1;
                    if ($multiple > 0 && $value % $multiple !== 0) {
                        $fail('Quantity must be a multiple of '.$multiple.'.');
                    }
                },
            ]
        ];
        $messages = [];
        
        // Add validation rules for modular products
        if ($this->product->hasModules()) {
            foreach ($this->product->modules as $index => $module) {
                $rules["selectedOptions.$index"] = 'required';
                $messages["selectedOptions.$index.required"] = 'Please select an option for ' . $module->name;
            }
        }
        
        $this->validate($rules, $messages);

        // Dispatch event to the main component to add the product to the cart
        $this->dispatch('add-to-order', productId: $this->product->id, quantity: $this->selectedQuantity, options: $this->selectedOptions);

        Flux::modal('add-to-cart')->close();
    }
}
