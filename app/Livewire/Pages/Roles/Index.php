<?php

namespace App\Livewire\Pages\Roles;

use App\Models\Client;
use Flux\Flux;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render()
    {
        return view('livewire.pages.roles.index');
    }

    #[Computed]
    public function roles()
    {
        return Role::query()
            ->withCount('users')
            ->where('name', 'like', '%' . $this->search . '%')
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('roles.show', ['role' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('roles.edit', ['role' => $id], navigate: true);
    }

    public function delete($id): void
    {
        // controllo che non ci siano utenti associati a questo ruolo
        if (Role::findOrFail($id)->users()->count() > 0) {
            Flux::toast(
                variant: 'error',
                text: 'You cannot delete this ROLE because it is assigned to one or more users.'
            );
            return;
        }

        Role::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The ROLE has been deleted.'
        );
    }
}
