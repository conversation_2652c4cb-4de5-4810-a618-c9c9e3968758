<?php

namespace App\Livewire\Pages\Partners;

use Flux\Flux;
use App\Models\Partner;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render()
    {
        return view('livewire.pages.partners.index');
    }

    #[Computed]
    public function partners()
    {
        return Partner::query()
            ->where(function ($query) {
                $query->where('company', 'like', '%' . $this->search . '%')
                      ->orWhere('type', 'like', '%' . $this->search . '%')
                      ->orWhere('commercial_category', 'like', '%' . $this->search . '%')
                      ->orWhere('priority', 'like', '%' . $this->search . '%')
                      ->orWhereHas('internalReferent', function ($query) {
                        $query->where('first_name', 'like', '%' . $this->search . '%')
                              ->orWhere('last_name', 'like', '%' . $this->search . '%');
                      });
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('partners.show', ['partner' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('partners.edit', ['partner' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Partner::findOrFail($id)->delete();
        
        Flux::toast(
            variant: 'success',
            text: 'The PARTNER has been deleted.'
        );
    }
}
