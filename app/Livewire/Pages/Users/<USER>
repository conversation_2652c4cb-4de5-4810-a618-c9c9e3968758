<?php

namespace App\Livewire\Pages\Users;

use Flux\Flux;
use App\Models\User;
use Livewire\Component;
use App\Enums\UserTypes;
use Illuminate\View\View;
use App\Livewire\Forms\UserForm;
use Spatie\Permission\Models\Role;

class Show extends Component
{
    public User $user;
    public UserForm $form;

    public function mount(): void
    {
        $this->form->setUser($this->user);
    }

    public function render(): View
    {
        return view('livewire.pages.users.show', [
            'userTypes' => UserTypes::cases(),
            'userRoles' => Role::all(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('users.edit', ['user' => $id], navigate: true);
    }

    public function delete(): void
    {
        if (auth()->user()->cannot('delete', $this->user)) {
            abort(403);
        }

        $this->user->delete();

        Flux::toast(
            variant: 'success',
            text: 'The USER has been deleted.'
        );

        $this->redirectRoute('users.index', navigate: true);
    }
}
