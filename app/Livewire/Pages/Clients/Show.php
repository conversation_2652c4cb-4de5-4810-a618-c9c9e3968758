<?php

namespace App\Livewire\Pages\Clients;

use Flux\Flux;
use App\Models\User;
use App\Models\Client;
use App\Models\Partner;
use Livewire\Component;
use Illuminate\View\View;
use App\Enums\ClientTypes;
use App\Models\PaymentTerm;
use App\Enums\DeliveryTerms;
use Livewire\WithPagination;
use App\Livewire\Forms\ClientForm;
use App\Enums\CommercialCategories;
use App\Enums\ClientPartnerPriorities;
use PrinsFrank\Standards\Country\CountryAlpha3;

class Show extends Component
{
    use WithPagination;

    public Client $client;
    public ClientForm $form;

    public function mount(): void
    {
        $this->form->setClient($this->client);
    }

    public function render(): View
    {
        return view('livewire.pages.clients.show', [
            'partners' => Partner::orderBy('id', 'desc')->get(),
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'clientTypes' => ClientTypes::cases(),
            'commercialCategories' => CommercialCategories::cases(),
            'deliveryTerms' => DeliveryTerms::cases(),
            'clientPriorities' => ClientPartnerPriorities::cases(),
            'countries' => CountryAlpha3::cases(),
            'countries_of_expertise' => CountryAlpha3::cases(),
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('clients.edit', ['client' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Client::findOrFail($id)->delete();
        Flux::toast(
            variant: 'success',
            text: 'The CLIENT has been deleted.'
        );

        $this->redirectRoute('clients.index', navigate: true);
    }

    // public function inheritBrands(): void
    // {
    //     if($this->client->partner) {
    //         $existingBrandIds = $this->client->brands->pluck('id')->toArray();
    //         $newBrandIds = $this->client->partner->brands->pluck('id')->toArray();
    //         $brandIdsToAttach = array_diff($newBrandIds, $existingBrandIds);
    //         $this->client->brands()->attach($brandIdsToAttach);
            
    //         Flux::toast(
    //             variant: 'success',
    //             text: 'The CLIENT has inherited brands from the PARTNER.'
    //         );

    //         $this->dispatch('refreshBrands');
    //     }
    //     else {
    //         Flux::toast(
    //             variant: 'warning',
    //             text: 'The CLIENT has no PARTNER to inherit brands from.'
    //         );
    //     }
    // }
}
