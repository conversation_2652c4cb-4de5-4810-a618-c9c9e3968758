<?php

namespace App\Livewire\Pages\Brands;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use Meilisearch\Client;
use App\Enums\BrandRating;
use App\Enums\BrandLeadTime;
use Livewire\WithPagination;
use App\Enums\BrandPriceRange;
use Livewire\Attributes\Computed;
use App\Enums\BrandPartnershipLevel;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public $selectedPriceRange = null;
    public $selectedRating = null;
    public $selectedPartnershipLevel = null;
    public $selectedLeadTime = null;

    private Client $msClient; // MeiliSearch client

    public function __construct()
    {
        $this->msClient = new Client(config('scout.host'), config('scout.key'));
    }

    public function render()
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            $this->selectedPriceRange ? 'price_range = "' . $this->selectedPriceRange . '"' : null,
            $this->selectedRating ? 'rating = "' . $this->selectedRating . '"' : null,
            $this->selectedPartnershipLevel ? 'partnership_level = "' . $this->selectedPartnershipLevel . '"' : null,
            $this->selectedLeadTime ? 'lead_time = "' . $this->selectedLeadTime . '"' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('brands')->search($this->search, [
            'facets' => ['price_range', 'rating', 'partnership_level', 'lead_time'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Extract facets from MeiliSearch response
        $facetsArray = $facets->getRaw();
        
        // Convert facets to enum instances
        $priceRanges = array_keys($facetsArray['facetDistribution']['price_range'] ?? []);
        $priceRanges = array_map(function ($value) {
            return BrandPriceRange::from($value);
        }, array_keys($facetsArray['facetDistribution']['price_range'] ?? []));

        // Convert facets to enum instances
        $ratings = array_keys($facetsArray['facetDistribution']['rating'] ?? []);
        $ratings = array_map(function ($value) {
            return BrandRating::from($value);
        }, array_keys($facetsArray['facetDistribution']['rating'] ?? []));

        // Convert facets to enum instances
        $partnershipLevels = array_keys($facetsArray['facetDistribution']['partnership_level'] ?? []);
        $partnershipLevels = array_map(function ($value) {
            return BrandPartnershipLevel::from($value);
        }, array_keys($facetsArray['facetDistribution']['partnership_level'] ?? []));

        // Convert facets to enum instances
        $leadTimes = array_keys($facetsArray['facetDistribution']['lead_time'] ?? []);
        $leadTimes = array_map(function ($value) {
            return BrandLeadTime::from($value);
        }, array_keys($facetsArray['facetDistribution']['lead_time'] ?? []));

        return view('livewire.pages.brands.index', [
            'priceRanges' => $priceRanges,
            'ratings' => $ratings,
            'partnershipLevels' => $partnershipLevels,
            'leadTimes' => $leadTimes,
        ]);
    }

    #[Computed]
    public function brands()
    {
        $query = Brand::search($this->search ?: '*')
            ->when($this->selectedPriceRange, function ($query) {
                $query->where('price_range', $this->selectedPriceRange);
            })
            ->when($this->selectedRating, function ($query) {
                $query->where('rating', $this->selectedRating);
            })
            ->when($this->selectedPartnershipLevel, function ($query) {
                $query->where('partnership_level', $this->selectedPartnershipLevel);
            })
            ->when($this->selectedLeadTime, function ($query) {
                $query->where('lead_time', $this->selectedLeadTime);
            });

        return $query
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function updatedSelectedPriceRange()
    {
        $this->resetPage();
    }

    public function updatedSelectedRating()
    {
        $this->resetPage();
    }

    public function updatedSelectedPartnershipLevel()
    {
        $this->resetPage();
    }

    public function updatedSelectedLeadTime()
    {
        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('brands.show', ['brand' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('brands.edit', ['brand' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Brand::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been deleted.'
        );
    }

    private function getPriceRanges(): array
    {
        return BrandPriceRange::cases();
    }

    private function getRatings(): array
    {
        return BrandRating::cases();
    }

    private function getPartnershipLevels(): array
    {
        return BrandPartnershipLevel::cases();
    }

    private function getLeadTimes(): array
    {
        return BrandLeadTime::cases();
    }
}
